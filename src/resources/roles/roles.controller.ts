import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseIntPipe,
  UseInterceptors,
  Query,
  UseGuards,
} from '@nestjs/common';
import { RolesService } from './roles.service';
import { CreateRoleDto } from './dto/create-role.dto';
import { UpdateRoleDto } from './dto/update-role.dto';
import { ApiBearerAuth, ApiBody, ApiConsumes } from '@nestjs/swagger';
import { AnyFilesInterceptor } from '@nestjs/platform-express';
import { DefaultQueryParams } from 'src/utils/default-query.decorator';
import type { DataParams } from 'src/types/params';
import { AuthGuard } from 'src/auth/auth.guard';

@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('roles')
export class RolesController {
  constructor(private readonly rolesService: RolesService) {}

  @Post()
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    type: 'object',
    description: 'Create a new role with the required fields.',
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string', example: 'Admin' },
        description: { type: 'string', example: 'Administrator role with full access' },
      },
      required: ['name'],
    },
  })
  @UseInterceptors(AnyFilesInterceptor())
  create(@Body() createRoleDto: CreateRoleDto) {
    return this.rolesService.create(createRoleDto);
  }

  // @Post(':id/permissions')
  // @ApiBody({
  //   schema: {
  //     type: 'object',
  //     properties: {
  //       permissionIds: {
  //         type: 'array',
  //         items: { type: 'number' },
  //         example: [1, 2, 3],
  //       },
  //     },
  //     required: ['permissionIds'],
  //   },
  // })
  // addPermissionsToRole(
  //   @Param('id', ParseIntPipe) id: number,
  //   @Body('permissionIds') permissionIds: number[],
  // ) {
  //   return this.rolesService.addPermissionsToRole(id, permissionIds);
  // }

  @Get()
  @DefaultQueryParams()
  findAll(@Query() pag: DataParams) {
    return this.rolesService.findAll(pag);
  }

  @Get(':id')
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.rolesService.findOne(id);
  }

  @Patch(':id')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    type: 'object',
    description: 'Create a new role with the required fields.',
    schema: {
      type: 'object',
      properties: {
        name: { type: 'string', example: 'Admin' },
        description: { type: 'string', example: 'Administrator role with full access' },
      },
      required: ['name' , 'description'],
    },
  })
  @UseInterceptors(AnyFilesInterceptor())
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateRoleDto: UpdateRoleDto,
  ) {
    return this.rolesService.update(id, updateRoleDto);
  }

  @Delete(':id')
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.rolesService.remove(id);
  }

  // @Delete(':id/permissions')
  // @ApiBody({
  //   schema: {
  //     type: 'object',
  //     properties: {
  //       permissionIds: {
  //         type: 'array',
  //         items: { type: 'number' },
  //         example: [1, 2],
  //       },
  //     },
  //     required: ['permissionIds'],
  //   },
  // })
  // removePermissionsFromRole(
  //   @Param('id', ParseIntPipe) id: number,
  //   @Body('permissionIds') permissionIds: number[],
  // ) {
  //   return this.rolesService.removePermissionsFromRole(id, permissionIds);
  // }
}
