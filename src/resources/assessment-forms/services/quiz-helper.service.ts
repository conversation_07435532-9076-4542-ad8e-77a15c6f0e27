import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, EntityManager, Not, IsNull } from 'typeorm';
import { Assessment } from '../entities/assessment.entity';
import { Submission } from '../entities/submission.entity';
import { ItemBlock } from '../entities/item-block.entity';
import { ItemBlockType } from '../enums/item-block-type.enum';
import { Question } from '../entities/question.entity';
import { Option } from '../entities/option.entity';

export interface AttemptInfo {
  completedAttempts: number;
  maxAttempts: number;
  hasUnlimitedAttempts: boolean;
  canAttempt: boolean;
}

export interface ScoreCalculationResult {
  score: number;
  totalScore: number;
  passed: boolean;
}

export interface TimeFormatOptions {
  format: 'human' | 'hms'; // 'human' = "1 ชั่วโมง 30 นาที", 'hms' = "01:30:00"
}

@Injectable()
export class QuizHelperService {
  constructor(
    @InjectRepository(Assessment)
    private assessmentRepository: Repository<Assessment>,
    @InjectRepository(ItemBlock)
    private itemBlockRepository: Repository<ItemBlock>,
    @InjectRepository(Submission)
    private submissionRepository: Repository<Submission>,
    @InjectRepository(Option)
    private optionRepository: Repository<Option>,
  ) {}

  /**
   * Find assessment by linkUrl with error handling
   */
  async findAssessmentByLinkUrl(linkUrl: string): Promise<Assessment> {
    const assessment = await this.assessmentRepository.findOne({
      where: { linkURL: linkUrl },
    });

    if (!assessment) {
      throw new NotFoundException(
        `Assessment with linkUrl "${linkUrl}" not found`,
      );
    }
    // find itemblock header
    const itemBlockHeader = await this.itemBlockRepository.findOne({
      where: { assessmentId: assessment.id, headerBody: true },
      relations: { headerBody: true },
    });
    assessment.itemBlocks = [];
    assessment.itemBlocks.push(itemBlockHeader);

    return assessment;
  }

  /**
   * Count completed submissions for a user and assessment
   */
  async getCompletedSubmissionsCount(
    assessmentId: number,
    userId: number,
    entityManager?: EntityManager,
  ): Promise<number> {
    const manager = entityManager || this.submissionRepository.manager;

    return await manager.count(Submission, {
      where: {
        assessmentId,
        userId,
        submitAt: Not(IsNull()), // Count only completed submissions
      },
    });
  }

  /**
   * Get user's completed submissions with full details
   */
  async getUserCompletedSubmissions(
    assessmentId: number,
    userId: number,
  ): Promise<Submission[]> {

    

    return await this.submissionRepository.find({
      where: {
        userId,
        assessmentId,
        submitAt: Not(IsNull()),
      },
      relations: {
        responses: {
          selectedOption: true,
          question: {
            itemBlock: true,
          },
        },
      },
      select: {
        id: true,
        startAt: true,
        endAt: true,
        submitAt: true,
        responses: {
          id: true,
          question: {
            id: true,
            score: true,
            itemBlock: {
              id: true,
              type: true,
            },
          },
          selectedOption: {
            id: true,
            value: true,
          },
        },
      },
      order: {
        submitAt: 'DESC',
      },
    });
  }

  /**
   * Calculate attempt information for a user and assessment
   */
  async calculateAttemptInfo(
    assessment: Assessment,
    userId?: number,
    entityManager?: EntityManager,
  ): Promise<AttemptInfo> {
    const attemptInfo: AttemptInfo = {
      completedAttempts: 0,
      maxAttempts: assessment.submitLimit,
      hasUnlimitedAttempts: assessment.submitLimit <= 0,
      canAttempt: true,
    };

    if (userId) {
      attemptInfo.completedAttempts = await this.getCompletedSubmissionsCount(
        assessment.id,
        userId,
        entityManager,
      );

      attemptInfo.canAttempt =
        attemptInfo.hasUnlimitedAttempts ||
        attemptInfo.completedAttempts < attemptInfo.maxAttempts;
    }

    return attemptInfo;
  }

  /**
   * Group responses by question ID
   */
  private groupResponsesByQuestion(responses: any[]): Map<number, {question: Question; responses: any[]}> {
    const grouped = new Map<number, {question: Question; responses: any[]}>();
    
    for (const response of responses || []) {
      const question = response?.question;
      if (!question) continue;
      
      const questionId = question.id;
      if (!grouped.has(questionId)) {
        grouped.set(questionId, { question, responses: [] });
      }
      grouped.get(questionId)?.responses.push(response);
    }
    
    return grouped;
  }

  /**
   * Calculate score for a RADIO type question
   */
  private calculateRadioScore(question: Question, responses: any[]): number {
    const response = responses[0];
    return (response?.selectedOption?.value === 1) ? (question.score ?? 0) : 0;
  }

  /**
   * Calculate score for a CHECKBOX type question
   */
  private  async calculateCheckboxScore(question: Question, responses: any[]): Promise<number> {
    // get all options
    
    const correctOptions = await this.optionRepository.find({
      where: {
        itemBlockId: question.itemBlockId,
        value: 1,
      },
    });

    // check if all correct options are selected
    const selectedOptions = responses.map((r) => r.selectedOption);
    const isAllCorrect = correctOptions.every((option) =>
      selectedOptions.some((selectedOption) => selectedOption.id === option.id),
    );
    return isAllCorrect ? question.score : 0;
  }

  /**
   * Calculate score for a TEXTFIELD type question
   */
  private calculateTextfieldScore(responses: any[]): number {
    return responses.reduce(
      (sum, r) => sum + (r?.selectedOption?.value ?? 0),
      0
    );
  }

  // total score
 async calculateTotalScore(assessmentId: number): Promise<number> {

    // get assessment with itemblock and questions get like super fast optimize
    const assessment = await this.assessmentRepository.findOne({
      where: { id: assessmentId },
      relations: { itemBlocks: { questions: true } },
      select: { id: true, itemBlocks: { id: true, questions: { id: true, score: true } } },
    });
    return assessment.itemBlocks.reduce(
      (sum, q) => sum + (q.questions.reduce((sum, q) => sum + (q.score ?? 0), 0)),
      0
    );
  }

  /**
   * Calculate score from submission responses
   */
   async calculateScore(
    submission: Submission,
    assessment: Assessment,
    totalScore: number,
  ): Promise<ScoreCalculationResult> {
    let score = 0;
    
    // Group responses by question to handle different question types
    const responsesByQuestion = this.groupResponsesByQuestion(submission.responses || []);
    console.log("submission.responses", submission.responses);
    console.log("responsesByQuestion", responsesByQuestion);

    // Process each question's responses
    for (const { question, responses } of responsesByQuestion.values()) {
      if (!question.itemBlock) continue;
      
      switch (question.itemBlock.type) {
        case ItemBlockType.RADIO:
          score += this.calculateRadioScore(question, responses);
          break;
          
        case ItemBlockType.CHECKBOX:
          score +=  await this.calculateCheckboxScore(question, responses);
          break;
          
        case ItemBlockType.TEXTFIELD:
          const textScore = this.calculateTextfieldScore(responses);
          if (textScore > 0) {
            score += textScore;
          }
          break;
      }
    }

    const passed = score >= totalScore * assessment.passRatio;

    return {
      score,
      totalScore,
      passed,
    };
  }

  /**
   * Format time duration with configurable output format
   */
  formatTime(
    seconds: number,
    options: TimeFormatOptions = { format: 'human' },
  ): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    if (options.format === 'hms') {
      // HH:MM:SS format
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }

    // Human readable format (default)
    const parts = [];
    if (hours > 0) parts.push(`${hours} ชั่วโมง`);
    if (minutes > 0) parts.push(`${minutes} นาที`);
    if (remainingSeconds > 0) parts.push(`${remainingSeconds} วินาที`);

    return parts.length > 0 ? parts.join(' ') : '0 วินาที';
  }

  /**
   * Calculate time spent between two dates
   */
  calculateTimeSpent(startAt: Date, endAt: Date): number {
    const timeSpentMs = endAt.getTime() - startAt.getTime();
    return Math.floor(timeSpentMs / 1000);
  }

  /**
   * Validate if user can attempt assessment (used in startAssessment)
   */
  async validateUserCanAttempt(
    assessment: Assessment,
    userId: number,
    entityManager: EntityManager,
  ): Promise<void> {
    if (assessment.submitLimit > 0) {
      const completedAttempts = await this.getCompletedSubmissionsCount(
        assessment.id,
        userId,
        entityManager,
      );

      if (completedAttempts >= assessment.submitLimit) {
        throw new Error(
          `Assessment submission limit reached. You have already completed ${completedAttempts} out of ${assessment.submitLimit} allowed attempts.`,
        );
      }
    }
  }
}
