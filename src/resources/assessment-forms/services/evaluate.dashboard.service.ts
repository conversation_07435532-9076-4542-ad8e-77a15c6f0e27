import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { Assessment } from '../entities/assessment.entity';
import { Response } from '../entities/response.entity';
import { ChartData } from '../dto/chart-data.dto';
import { ApiService } from 'src/api/api.service';
import * as ExcelJS from 'exceljs';
import { Response as Res } from 'express';



@Injectable()
export class EvaluateDashBoardService {
  constructor(
    @InjectEntityManager()
    private entityManager: EntityManager,
    @InjectRepository(Assessment)
    private assessmentRepo: Repository<Assessment>,
    @InjectRepository(Response)
    private responseRepo: Repository<Response>,
    private apiService: ApiService,
  ) {}

async getChartData(assessmentId: number): Promise<ChartData[]> {
  const chartDatas: ChartData[] = [];

  interface FileData {
    files: { fileName: string; downloadName?: string }[];
  }
const assessment = await this.assessmentRepo
  .createQueryBuilder('a')
  .leftJoinAndSelect('a.itemBlocks', 'ib')
  .leftJoinAndSelect('ib.headerBody', 'h')
  .leftJoinAndSelect('ib.questions', 'q')
  .leftJoinAndSelect('q.responses', 'r')
  .leftJoinAndSelect('r.submission', 's')
  .leftJoinAndSelect('s.user', 'u')
  .leftJoinAndSelect('r.selectedOption', 'so')
  .leftJoinAndSelect('ib.options', 'o', 'o.id = so.id')
  .where('a.id = :id', { id: assessmentId })
  .andWhere('s.submitAt IS NOT NULL')
  .andWhere('ib.type NOT IN (:...types)', { types: ['IMAGE'] })
  .orderBy('q.sequence', 'ASC')
  .addOrderBy('o.sequence', 'ASC')
  .getOne();




  if (!assessment) {
    throw new NotFoundException(`Assessment with ID ${assessmentId} not found`);
  }

  const maxSection = Math.max(...assessment.itemBlocks.map((ib) => ib.section));
  const processedHeaderSections = new Set<number>();

  for (const itemBlock of assessment.itemBlocks) {
    const sectionLabel = `${itemBlock.section},${maxSection}`;

    if (
      itemBlock.type === 'HEADER' &&
      !processedHeaderSections.has(itemBlock.section) &&
      itemBlock.section !== 1
    ) {
      chartDatas.push({
        section: sectionLabel,
        sequence: itemBlock.sequence || 1,
        title: itemBlock.headerBody?.title || `หัวข้อที่ ${itemBlock.section}`,
        labels: [],
        datasets: [{ label: '', values: [] }],
        type: 'header',
      });
      processedHeaderSections.add(itemBlock.section);
      continue;
    }

    let type: string;
    if (itemBlock.type === 'UPLOAD') {
      type = 'image';
    } else if (['RADIO', 'CHECKBOX', 'GRID'].includes(itemBlock.type)) {
      type = 'choice';
    } else {
      type = 'text';
    }

    let questions: any[] = [];
    let title = '';

    if (itemBlock.type === 'GRID') {
      const headerQuestion = itemBlock.questions.find((q) => q.isHeader);
      questions = itemBlock.questions.filter((q) => !q.isHeader);
      title = headerQuestion?.questionText || '';
    } else {
      questions = itemBlock.questions.filter((q) => q.isHeader);
      title = questions.length > 0 ? questions[0].questionText : '';
    }

    if (type === 'image') {
      const options = itemBlock.options
        ? itemBlock.options.sort((a, b) => a.sequence - b.sequence)
        : [];

      const files: FileData['files'] = options.map((option) => ({
        fileName: option.imagePath || option.optionText || 'ตัวเลือก....',
      }));

      let viewUrls: string[] = options.map(
        (option) => option.imagePath || option.optionText || 'ตัวเลือก....',
      );

      try {
        const response = await this.apiService.getPublicFiles(files);
        if (response?.status === 'success' && response.result) {
          viewUrls = options.map((_, index) => {
            const fileKey = `file_${index + 1}`;
            return response.result[fileKey]?.view || viewUrls[index];
          });
        }
      } catch (error) {
        console.error('ApiService Error:', error.message);
      }

      const responses = questions
        .flatMap((q) => q.responses || [])
        .filter((r) => r.submission?.user?.name && r.selectedOption)
        .sort((a, b) => {
          const optionA = options.find((o) => o.id === a.selectedOption.id);
          const optionB = options.find((o) => o.id === b.selectedOption.id);
          return (optionA?.sequence || 0) - (optionB?.sequence || 0);
        });

      const userNames = responses.map((r) => r.submission.user.name);
      const datasets = options.map((option, index) => ({
        label: viewUrls[index],
        values: [],
      }));

      if (datasets.length > 0) {
        chartDatas.push({
          section: sectionLabel,
          sequence: itemBlock.sequence,
          title: title,
          labels: userNames,
          datasets,
          type: 'image',
        });
      }
    } else if (type === 'choice') {
      const sortedQuestions = questions.sort((a, b) => a.sequence - b.sequence);

      const options = itemBlock.options
        ? itemBlock.options.sort((a, b) => a.sequence - b.sequence)
        : [];

      const labels = sortedQuestions.map((q) => q.questionText);
      const datasets = options.map((option) => {
        const values = sortedQuestions.map((question) => {
          const responseCount = question.responses
            ? question.responses.filter(
                (r) => r.selectedOption && r.selectedOption.id === option.id,
              ).length
            : 0;
          return responseCount;
        });
        return { label: option.optionText, values };
      });

      chartDatas.push({
        section: sectionLabel,
        sequence: itemBlock.sequence,
        title,
        labels,
        datasets,
        type,
      });
    } else {
      // type === 'text'
      const allResponses = questions.flatMap((q) => q.responses || []);
      const options = itemBlock.options
        ? itemBlock.options.sort((a, b) => a.sequence - b.sequence)
        : [];

      const datasets = options.map((option) => {
        const count = allResponses.filter(
          (r) => r.selectedOption && r.selectedOption.id === option.id,
        ).length;
        return {
          label: option.optionText,
          values: [count],
        };
      });

      const allLabelsAreNumbers = datasets.every((d) => !isNaN(Number(d.label)));

      if (datasets.length > 0) {
        if (allLabelsAreNumbers) {
          chartDatas.push({
            section: sectionLabel,
            sequence: itemBlock.sequence,
            title: '',
            labels: [title],
            datasets,
            type: 'choice',
          });
        } else {
          chartDatas.push({
            section: sectionLabel,
            sequence: itemBlock.sequence,
            title,
            labels: [],
            datasets,
            type,
          });
        }
      }
    }
  }

  return chartDatas.sort((a, b) => {
    const sectionA = parseInt(a.section.split(',')[0], 10);
    const sectionB = parseInt(b.section.split(',')[0], 10);
    if (sectionA !== sectionB) {
      return sectionA - sectionB;
    }
    return a.sequence - b.sequence;
  });
}
  


async exportAssessmentToExcel(assessmentId: number, res: Res): Promise<void> {
  // ตรวจสอบว่า assessmentId มีค่าหรือไม่
  if (!assessmentId) {
    throw new NotFoundException('Assessment ID is required');
  }

  // ดึงข้อมูล assessment
  const assessment = await this.assessmentRepo.findOne({ where: { id: assessmentId } });
  if (!assessment) {
    throw new NotFoundException(`Assessment with ID ${assessmentId} not found`);
  }

  // Query สำหรับดึง header ของคำถาม (รวมคำถามย่อยใน GRID)
  const questionHeaders = await this.entityManager.query(`
    WITH HeaderQuestions AS (
      SELECT DISTINCT
        CASE 
          WHEN ib.type = 'GRID' THEN 
            CASE 
              WHEN qh.questionText IS NOT NULL THEN CONCAT(q.questionText, ' [', qh.questionText, ']')
              ELSE q.questionText
            END
          ELSE
            CASE 
              WHEN qh.questionText IS NOT NULL THEN CONCAT(qh.questionText, ' [', q.questionText, ']')
              ELSE q.questionText
            END
        END AS questionText,
        ib.id AS itemBlockId,
        ib.sequence AS itemBlockSequence,
        COALESCE(qh.id, q.id) AS questionId
      FROM item_blocks ib
      JOIN questions q ON q.itemBlockId = ib.id AND q.isHeader = 1
      LEFT JOIN questions qh ON qh.itemBlockId = ib.id AND qh.isHeader = 0
      WHERE ib.assessmentId = ${assessmentId}
        AND ib.type NOT IN ('IMAGE', 'HEADER', 'UPLOAD')
    )
    SELECT questionText, itemBlockId, itemBlockSequence, questionId
    FROM HeaderQuestions
    ORDER BY itemBlockId, itemBlockSequence
  `);

  if (!questionHeaders.length) {
    throw new NotFoundException('No valid questions found for this assessment');
  }

  // Query สำหรับดึงคำตอบ
  const responseData = await this.entityManager.query(`
    SELECT 
  s.endAt,
  s.submitAt,
  u.name,
  CASE 
    WHEN ib.type = 'CHECKBOX' THEN GROUP_CONCAT(o.optionText ORDER BY o.optionText SEPARATOR ', ')
    WHEN ib.type = 'GRID' THEN o.optionText
    ELSE o.optionText
  END AS optionText,
  u.id AS userId,
  ib.id AS itemBlockId,
  q.id AS questionId
FROM item_blocks ib
JOIN questions q ON q.itemBlockId = ib.id
JOIN responses r ON r.questionId = q.id
JOIN options o ON r.selectedOptionId = o.id
JOIN submissions s ON r.submissionId = s.id
JOIN users u ON s.userId = u.id
WHERE ib.assessmentId = ${assessmentId}
  AND ib.type NOT IN ('IMAGE', 'HEADER', 'UPLOAD')
  AND s.endAt IS NOT NULL
GROUP BY 
  s.submitAt, s.endAt, u.name, ib.id, u.id, q.id,
  CASE WHEN ib.type IN ('CHECKBOX', 'GRID') THEN 0 ELSE o.optionText END
ORDER BY s.submitAt, u.id, ib.id, q.id;


  `);

  console.log('questionHeaders:', questionHeaders);
  console.log('responseData:', responseData);

  // สร้าง header ของ Excel
  const headerRow = ['เวลา', 'ลำดับ', 'ชื่อ', ...questionHeaders.map((q: { questionText: string }) => q.questionText)];
  const rawName = assessment.name || 'assessment';
  const safeName = rawName
    .replace(/[\/\\:*?"<>|]/g, '')
    .replace(/\s+/g, '_')
    .trim() || 'assessment';
  const encodedFileName = encodeURIComponent(`${safeName}.xlsx`);

  // สร้าง workbook และ worksheet
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Assessment Results');
  const header = worksheet.addRow(headerRow);
  header.font = { bold: true, size: 14 };
  header.alignment = { vertical: 'middle', horizontal: 'center' };
  header.height = 20;

  worksheet.getColumn(1).numFmt = 'yyyy-mm-dd hh:mm:ss';

  // จัดกลุ่มคำตอบตาม userId และ endAt
  const responseMap = new Map<
    string,
    { endAt: string; name: string; responses: { itemBlockId: number; questionId: number; optionText: string }[] }
  >();
  responseData.forEach(
    (row: { endAt: string; name: string; optionText: string; userId: number; itemBlockId: number; questionId: number }) => {
      const key = `${row.userId}_${row.endAt}`;
      if (!responseMap.has(key)) {
        responseMap.set(key, { endAt: row.endAt, name: row.name, responses: [] });
      }
      responseMap.get(key)!.responses.push({
        itemBlockId: row.itemBlockId,
        questionId: row.questionId,
        optionText: row.optionText || '',
      });
    },
  );

  const sortedResponses = Array.from(responseMap.entries())
  .map(([key, data]) => ({
    userId: parseInt(key.split('_')[0], 10),
    endAt: data.endAt,
    name: data.name,
    responses: data.responses,
    submitAt: key.split('_')[1], // ดึง submitAt มาใช้เรียง
  }))
  .sort((a, b) => new Date(a.submitAt).getTime() - new Date(b.submitAt).getTime());


  // เพิ่มแถวข้อมูลลงใน worksheet
  sortedResponses.forEach((data, index) => {
    const row = [new Date(data.endAt + 'Z'), index + 1, data.name];
    questionHeaders.forEach((header: { itemBlockId: number; questionId: number }) => {
      const response = data.responses.find(
        (r) => r.itemBlockId === header.itemBlockId && r.questionId === header.questionId,
      );
      row.push(response ? response.optionText : '');
    });
    worksheet.addRow(row);
  });

  // ปรับขนาดคอลัมน์ให้เหมาะสมกับความยาวของข้อมูล
  worksheet.columns.forEach((column) => {
  // ถ้าเป็นคอลัมน์แรก (เวลา)
  if (column.number === 1) {
    column.width = 22; // กว้างพอสำหรับ "2025-06-10 07:14:40"
    return;
  }

  // คำนวณความกว้างตามค่าภายในเซลล์
  let maxLength = 10;
  column.eachCell({ includeEmpty: true }, (cell) => {
    const cellValue = cell.value?.toString() || '';
    maxLength = Math.max(maxLength, cellValue.length);
  });

  column.width = maxLength + 2;
});


  // ตั้งค่า header สำหรับ response
  res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
  res.setHeader('Content-Disposition', `attachment; filename*=UTF-8''${encodedFileName}`);

  // ส่งไฟล์ Excel ไปยัง client
  await workbook.xlsx.write(res);
}


  async getNumberOfResponses(assessmentId: number): Promise<number> {
  const result = await this.assessmentRepo.query(
    `SELECT A.name, COUNT(*) as number
     FROM assessments AS A
     JOIN submissions AS S ON A.id = S.assessmentId
     WHERE A.id = ${assessmentId}
       AND S.submitAt IS NOT NULL`
  );
  return result[0]; // แก้ตรงนี้เพื่อ return number โดยตรง
}

}