import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SubmissionsService } from './submissions.service';
import { Submission } from '../entities/submission.entity';
import { Response } from '../entities/response.entity';
import { SubmissionsController } from './submissions.controller';
import { Assessment } from '../entities/assessment.entity';
import { QuizHelperService } from '../services/quiz-helper.service';
import { ItemBlock } from '../entities/item-block.entity';
import { Option } from '../entities/option.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Submission,
      Response, // <-- Added Response entity
      Assessment, // <-- Added Assessment entity for QuizHelperService
      ItemBlock,
      Option,
    ]),
  ],
  controllers: [SubmissionsController],
  providers: [SubmissionsService, QuizHelperService],
  exports: [SubmissionsService],
})
export class SubmissionsModule {}
