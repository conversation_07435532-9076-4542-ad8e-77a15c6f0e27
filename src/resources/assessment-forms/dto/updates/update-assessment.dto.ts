import { ApiProperty, PartialType } from '@nestjs/swagger';
import { CreateAssessmentDto } from '../creates/create-assessment.dto';
import {
  IsBoolean,
  IsDate,
  isNumber,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';
import { Transform } from 'class-transformer';

export class UpdateAssessmentDto extends PartialType(CreateAssessmentDto) {
  @ApiProperty({
    description: 'ชื่อแบบประเมิน',
    example: 'Updated Assessment Name',
    required: false,
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: 'จำนวนครั้งที่อนุญาตให้ส่งแบบประเมิน',
    example: 1,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  submitLimit?: number;

  @ApiProperty({
    description: 'เวลาที่อนุญาต (วินาที)',
    example: 1000,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  timeout?: number;

  @ApiProperty({
    description: 'วันที่เริ่มต้น',
    type: 'string',
    format: 'date-time',
    example: new Date(),
    required: false,
  })
  @IsDate()
  @IsOptional()
  startAt: Date;

  @ApiProperty({
    description: 'วันที่สิ้นสุด',
    type: 'string',
    format: 'date-time',
    example: (() => {
      const date = new Date();
      date.setDate(date.getDate() + 1);
      return date;
    })(),
    required: false,
  })
  @IsDate()
  @IsOptional()
  endAt: Date;

  @ApiProperty({
    description: 'สถานะการเปิดใช้งาน',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(
    ({ value }) => {
      if (typeof value === 'string') {
        return value.toLowerCase() === 'true';
      }
      return Boolean(value);
    },
    { toClassOnly: true },
  )
  status?: boolean;

  @ApiProperty({
    description: 'คะแนนรวม (สำหรับ Quiz เท่านั้น)',
    example: 10,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  totalScore?: number;

  @ApiProperty({
    description: 'อัตราส่วนการผ่าน (เช่น 0.5, 1.5)',
    example: 0.5,
    type: 'number',
    format: 'float',
    required: false,
  })
  @IsNumber()
  @IsOptional()
  passRatio?: number;

  @ApiProperty({
    description: 'เป็นต้นแบบหรือไม่',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(
    ({ value }) => {
      if (typeof value === 'string') {
        return value.toLowerCase() === 'true';
      }
      return Boolean(value);
    },
    { toClassOnly: true },
  )
  isPrototype?: boolean;

  @ApiProperty({
    description: 'อนุญาตให้แก้ไขคำตอบหรือไม่',
    example: false,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  responseEdit?: boolean;

  @ApiProperty({
    description: 'URL ลิงก์ของแบบประเมิน',
    example: 'https://example.com/assessment',
    required: false,
  })
  @IsString()
  @IsOptional()
  linkURL?: string;
}
