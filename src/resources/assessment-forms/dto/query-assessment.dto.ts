import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { AssessmentType } from '../enums/assessment-type.enum';

export class QueryAssessmentDto {
  @ApiProperty({
    description: 'ประเภทของแบบประเมิน',
    enum: AssessmentType,
    required: false,
  })
  @IsOptional()
  @IsEnum(AssessmentType)
  type?: AssessmentType;

  @ApiProperty({
    description: 'ค้นหาด้วยชื่อแบบประเมิน',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({
    description: 'รหัสผู้ใช้',
    required: false,
  })
  @IsOptional()
  userId?: number;
}
