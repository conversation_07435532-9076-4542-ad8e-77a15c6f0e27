// file management controller file

import { <PERSON>, Body, Post } from '@nestjs/common';
import { FileUploadService } from './file-upload.service';
import { ApiBody, ApiTags } from '@nestjs/swagger';

@ApiTags('File Upload')
@Controller('file-upload')
export class FileUploadController {
  constructor(private readonly fileUploadService: FileUploadService) {}

  // get public file by post path body
  @Post('public-file-path')
  @ApiBody({
    type: 'object',
    required: true,
    schema: {
      properties: {
        imagePath: {
          type: 'string',
          description: 'Image path',
        },
      },
    },
  })
  async getPublicFile(@Body() imagePath: { imagePath: string }) {
    return this.fileUploadService.processImagePath(imagePath.imagePath);
  }

}
