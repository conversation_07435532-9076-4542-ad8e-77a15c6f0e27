import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
  InternalServerErrorException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In, Like } from 'typeorm';
import { User } from './entities/user.entity';
import { CreateUserDto } from './dto/create-user.dto';
import {
  UpdateUserDto,
  UpdateUserPasswordDto,
  UpdateUserRolesDto,
} from './dto/update-user.dto';
import { Role } from 'src/resources/roles/entities/role.entity';
import * as bcrypt from 'bcrypt';
import { instanceToPlain } from 'class-transformer';
import type { DataParams, DataResponse } from 'src/types/params';

const SALT_ROUNDS = 10; // For bcrypt
export interface UserListItem {
  id: number;
  name: string;
  email: string;
  roles: any[];
  password: string;
}

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>,
  ) {}

  async create(createUserDto: CreateUserDto): Promise<Omit<User, 'password'>> {
    const { email, password, roleIds, ...userData } = createUserDto;

    const existingUser = await this.userRepository.findOneBy({ email });
    if (existingUser) {
      throw new ConflictException(`User with email "${email}" already exists.`);
    }

    const hashedPassword = await bcrypt.hash(password, SALT_ROUNDS);
    if (!hashedPassword) {
      throw new InternalServerErrorException('Failed to hash password.');
    }

    const user = this.userRepository.create({
      ...userData,
      email,
      password: hashedPassword,
    });

    if (roleIds && roleIds.length > 0) {
      const roles = await this.roleRepository.findBy({ id: In(roleIds) });
      if (roles.length !== roleIds.length) {
        const foundRoleIdsSet = new Set(roles.map((role) => role.id));
        const notFoundIds = roleIds.filter((id) => !foundRoleIdsSet.has(id));
        throw new BadRequestException(
          `One or more roles not found: IDs ${notFoundIds.join(', ')}.`,
        );
      }
      user.roles = roles;
      console.log('user.roles', user.roles);
    } else {
      // Optional: Assign a default role if no roleIds are provided
      // const defaultRole = await this.roleRepository.findOneBy({ name: 'student' }); // Example
      // if (defaultRole) user.roles = [defaultRole];
      user.roles = []; // Or ensure it's initialized if not eager loaded sometimes
    }

    const savedUser = await this.userRepository.save(user);
    return instanceToPlain(savedUser) as Omit<User, 'password'>;
  }

  async findAll(pag: DataParams): Promise<{
    data: UserListItem[];
    total: number;
    curPage: number;
    hasPrev: boolean;
    hasNext: boolean;
  }> {
    const [users, total] = await this.userRepository.findAndCount({
      relations: ['roles'],
      where: pag.search
        ? [
            { name: Like(`%${pag.search}%`) },
            { email: Like(`%${pag.search}%`) },
          ]
        : {},
      order: {
        [pag.sortBy || 'id']: pag.order || 'ASC',
      },
      skip: (pag.page - 1) * pag.limit,
      take: pag.limit,
      cache: true,
    });

    return {
      data: users.map((user: any) => ({
        id: user.id,
        name: user.name,
        email: user.email,
        roles: user.roles,
        password: user.password,
      })),
      total,
      curPage: pag.page,
      hasPrev: pag.page > 1,
      hasNext: pag.page * pag.limit < total,
    };
  }

  async findOne(
    id: number,
    loadRelations: string[] = ['roles'],
  ): Promise<Omit<User, 'password'>> {
    console.log('GET FIND ONE');

    const user = await this.userRepository.findOne({
      where: { id },
      relations: loadRelations,
    });
    if (!user) {
      throw new NotFoundException(`User with ID "${id}" not found`);
    }
    // console.log('USER RETURN', instanceToPlain(user) as Omit<User, 'password'>);
    return user;
  }

  async findOneByEmail(
    email: string,
    loadRelations: string[] = ['roles'],
  ): Promise<User | null> {
    // This method might be used internally for auth, so it returns the full User object including password
    return this.userRepository.findOne({
      where: { email },
      relations: loadRelations,
    });
  }

  async update(
    id: number,
    updateUserDto: UpdateUserDto & {
      currentPassword?: string;
      newPassword?: string;
      roleIds?: number[];
    },
  ): Promise<Omit<User, 'password'>> {
    const user = await this.userRepository.findOne({
      where: { id },
      relations: ['roles'],
    });

    if (!user) {
      throw new NotFoundException(`User with ID "${id}" not found.`);
    }

    // Email update check
    if (updateUserDto.email && updateUserDto.email !== user.email) {
      const existingUserWithNewEmail = await this.userRepository.findOneBy({
        email: updateUserDto.email,
      });
      if (existingUserWithNewEmail && existingUserWithNewEmail.id !== id) {
        throw new ConflictException(
          `User with email "${updateUserDto.email}" already exists.`,
        );
      }
    }
    const { newPassword, currentPassword, roleIds, ...otherFields } =
      updateUserDto;
    this.userRepository.merge(user, otherFields);
    // Password update
    if (updateUserDto.newPassword) {
      console.log('in password');
      console.log('PASSWORD ', updateUserDto.newPassword);
      const isTestOldPasswordMatching = await bcrypt.compare(
        updateUserDto.currentPassword,
        user.password,
      );
      console.log('Boolean isMatch', isTestOldPasswordMatching);

      if (!updateUserDto.currentPassword) {
        throw new BadRequestException(
          'Current password is required to change password.',
        );
      }
      const isOldPasswordMatching = await bcrypt.compare(
        updateUserDto.currentPassword,
        user.password,
      );
      if (!isOldPasswordMatching) {
        throw new BadRequestException('Old password does not match.');
      }
      if (updateUserDto.newPassword.length < 8) {
        throw new BadRequestException(
          'New password must be at least 8 characters long.',
        );
      }

      user.password = await bcrypt.hash(updateUserDto.newPassword, SALT_ROUNDS);
      console.log('USER PASSWORD', user.password);
    }

    // Roles update
    if (updateUserDto.roleIds) {
      // Clear existing roles
      user.roles = [];

      if (updateUserDto.roleIds.length > 0) {
        const newRoles = await this.roleRepository.findBy({
          id: In(updateUserDto.roleIds),
        });
        if (newRoles.length !== updateUserDto.roleIds.length) {
          const foundRoleIds = newRoles.map((r) => r.id);
          const notFoundIds = updateUserDto.roleIds.filter(
            (id) => !foundRoleIds.includes(id),
          );
          throw new BadRequestException(
            `One or more roles not found: IDs ${notFoundIds.join(', ')}.`,
          );
        }
        user.roles = newRoles;
        console.log('In update Role', user.roles);
      }
    }

    // Merge other fields (excluding password, roles)

    const updatedUser = await this.userRepository.save(user);

    return instanceToPlain(updatedUser) as Omit<User, 'password'>;
  }

  async remove(id: number): Promise<void> {
    const user = await this.userRepository.findOneBy({ id });
    if (!user) {
      throw new NotFoundException(`User with ID "${id}" not found.`);
    }
    await this.userRepository.remove(user);
  }
}
