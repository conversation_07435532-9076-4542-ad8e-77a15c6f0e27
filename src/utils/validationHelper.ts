import type { ItemBlock, Assessment } from 'src/types/models';

// โครงสร้างผลลัพธ์การตรวจสอบความถูกต้อง
export interface ValidationResult {
  valid: boolean;
  missing: string[];
}

// โครงสร้างผลลัพธ์การตรวจสอบการลบ block
export interface BlockDeletionValidation {
  canDelete: boolean;
  issues: string[];
}

// โครงสร้างผลลัพธ์การตรวจสอบหลังลบ block
export interface PostDeletionValidation {
  success: boolean;
  issues: string[];
}

// ตรวจสอบว่า assessment มี id และ itemBlocks ที่จำเป็นครบหรือไม่
export function validateAssessmentIds(currentAssessment: Assessment | null): ValidationResult {
  const missing: string[] = [];

  if (!currentAssessment?.id) {
    missing.push('assessmentId');
  }

  if (!currentAssessment?.itemBlocks || currentAssessment.itemBlocks.length === 0) {
    missing.push('itemBlocks');
  } else {
    currentAssessment.itemBlocks.forEach((block, index) => {
      if (!block.id) {
        missing.push(`itemBlock[${index}].id`);
      }
      if (!block.assessmentId) {
        missing.push(`itemBlock[${index}].assessmentId`);
      }
    });
  }

  return {
    valid: missing.length === 0,
    missing,
  };
}

// ตรวจสอบว่า block สามารถลบได้อย่างปลอดภัยหรือไม่
export function validateBlockForDeletion(
  blockId: number,
  currentAssessment: Assessment | null,
): BlockDeletionValidation {
  const issues: string[] = [];

  if (!currentAssessment) {
    issues.push('No current assessment loaded');
    return { canDelete: false, issues };
  }

  if (!blockId) {
    issues.push('Invalid block ID provided');
    return { canDelete: false, issues };
  }

  const targetBlock = currentAssessment.itemBlocks?.find((block) => block.id === blockId);
  if (!targetBlock) {
    issues.push(`Block with ID ${blockId} not found in current assessment`);
    return { canDelete: false, issues };
  }

  // ตรวจสอบ header block เพิ่มเติม
  if (targetBlock.type === 'HEADER' && !targetBlock.headerBody) {
    issues.push('Header block missing headerBody data');
  }

  // ตรวจสอบ reference ที่ orphan
  if (targetBlock.assessmentId !== currentAssessment.id) {
    issues.push(
      `Block assessmentId (${targetBlock.assessmentId}) does not match current assessment ID (${currentAssessment.id})`,
    );
  }

  return {
    canDelete: issues.length === 0,
    issues,
  };
}

// ตรวจสอบสถานะหลังลบ block
export function validatePostDeletionState(
  deletedBlockId: number,
  currentAssessment: Assessment | null,
): PostDeletionValidation {
  const issues: string[] = [];

  if (!currentAssessment) {
    issues.push('No current assessment loaded');
    return { success: false, issues };
  }

  // ตรวจสอบว่า block ที่ลบยังอยู่ใน assessment หรือไม่
  const blockStillExists = currentAssessment.itemBlocks?.some(
    (block) => block.id === deletedBlockId,
  );
  if (blockStillExists) {
    issues.push(`Block with ID ${deletedBlockId} still exists in assessment after deletion`);
  }

  // ตรวจสอบ orphaned questions
  const orphanedQuestions = currentAssessment.itemBlocks?.some((block) =>
    block.questions?.some((question) => question.itemBlockId === deletedBlockId),
  );
  if (orphanedQuestions) {
    issues.push(`Found orphaned questions referencing deleted block ID ${deletedBlockId}`);
  }

  // ตรวจสอบ orphaned options
  const orphanedOptions = currentAssessment.itemBlocks?.some((block) =>
    block.options?.some((option) => option.itemBlockId === deletedBlockId),
  );
  if (orphanedOptions) {
    issues.push(`Found orphaned options referencing deleted block ID ${deletedBlockId}`);
  }

  return {
    success: issues.length === 0,
    issues,
  };
}

// ตรวจสอบว่า assessmentId พร้อมใช้งานสำหรับ operation หรือไม่
export function validateAssessmentIdForOperation(
  assessmentId: number | null | undefined,
  operationName: string,
): { valid: boolean; error?: string } {
  if (!assessmentId) {
    return {
      valid: false,
      error: `Assessment ID is required for ${operationName}`,
    };
  }

  return { valid: true };
}

// ตรวจสอบข้อมูลก่อนสร้าง block ใหม่
export function validateBlockCreationData(data: {
  assessmentId?: number | null;
  sequence?: number;
  section?: number;
  type?: string;
}): { valid: boolean; issues: string[] } {
  const issues: string[] = [];

  if (!data.assessmentId) {
    issues.push('assessmentId is required');
  }

  if (data.sequence !== undefined && data.sequence < 1) {
    issues.push('sequence must be greater than 0');
  }

  if (data.section !== undefined && data.section < 1) {
    issues.push('section must be greater than 0');
  }

  if (!data.type) {
    issues.push('type is required');
  }

  return {
    valid: issues.length === 0,
    issues,
  };
}

// ตรวจสอบความสอดคล้องของ blocks array
export function validateBlocksConsistency(blocks: ItemBlock[]): {
  valid: boolean;
  issues: string[];
} {
  const issues: string[] = [];

  // ตรวจสอบ id ซ้ำ
  const ids = blocks.map((block) => block.id).filter((id) => id !== undefined);
  const uniqueIds = new Set(ids);
  if (ids.length !== uniqueIds.size) {
    issues.push('Duplicate block IDs found');
  }

  // ตรวจสอบความต่อเนื่องของ sequence
  const sequences = blocks.map((block) => block.sequence).sort((a, b) => a - b);
  for (let i = 0; i < sequences.length; i++) {
    if (sequences[i] !== i + 1) {
      issues.push(`Sequence gap or duplicate found at position ${i + 1}`);
      break;
    }
  }

  // ตรวจสอบความต่อเนื่องของ section (เฉพาะ header block)
  const headerBlocks = blocks.filter((block) => block.type === 'HEADER');
  const sections = headerBlocks.map((block) => block.section).sort((a, b) => a - b);
  for (let i = 0; i < sections.length; i++) {
    if (sections[i] !== i + 1) {
      issues.push(`Section gap or duplicate found at section ${i + 1}`);
      break;
    }
  }

  return {
    valid: issues.length === 0,
    issues,
  };
}
