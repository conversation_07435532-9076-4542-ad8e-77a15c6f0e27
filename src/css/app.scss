// * app global css in SCSS form

// .body--light {
//   background-color: #f0f0f0;
// }

// .body--dark {
//   background-color: #121212;
// }

@import url('./font.css');

* {
  font-family: 'Sarabun', sans-serif;
}

.card-form {
  min-width: 500px;
}

.header-dialog {
  font-size: 30px;
  font-weight: medium;
}

.header {
  font-size: 20px;
}

.body {
  font-size: 18px;
}

.sub-body {
  font-size: 16px;
  color: #dddddd;
}

.container {
  width: 100%;
  max-width: 1024px;
}

.wrapper {
  max-width: 1024px;
  margin: auto;
}

.bg-page-primary {
  background: $surface-primary;
}

.evaluate-item {
  margin: auto;
  min-height: 200px;
  max-width: 900px;
  min-width: 900px;
  margin-bottom: 16px;
  width: 100%;
}

.evaluate-get {
  margin: auto;
  max-width: 900px;
  min-width: 900px;
  margin-bottom: 16px;
  width: 100%;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

@media (max-width: 1023.98px) {
  .evaluate-item {
    min-width: 600px;
  }

  .evaluate-get {
    min-width: 600px;
  }
}

.q-btn {
  border-radius: $generic-border-radius;
  justify-content: center;
  align-items: center;
}

.q-btn::before {
  box-shadow: none;
}

.q-card {
  box-shadow: none;
  outline: $surface;
  border: 1px solid $surface;
}

.text-body1 {
  font-size: 16px;
}

.q-header {
  background: $secondary;
}
.q-table thead tr {
  background-color: var(--q-primary) !important;
  color: black !important;
  text-align: center !important;
}
