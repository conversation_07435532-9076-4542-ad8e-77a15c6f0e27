// Custom SVG icon mapping for Quasar components
import { boot } from 'quasar/wrappers';

// Icon map for custom SVGs in the public/svg folder
const customSvgIcons: Record<string, string> = {
  'app:add-circle': 'img:/svg/add-circle.svg',
  'app:image': 'img:/svg/image.svg',
  'app:text': 'img:/svg/text.svg',
  'app:section': 'img:/svg/section.svg',
  'app:quiz': 'img:/svg/quiz.svg',
  'app:test-quiz': 'img:/svg/test-quiz.svg',
};

export default boot(({ app }) => {
  // Get Quasar instance
  const $q = app.config.globalProperties.$q;

  // Set the icon mapping function
  $q.iconMapFn = (iconName: string) => {
    // If the icon name starts with 'app:', map it to our custom SVG icons
    if (iconName.startsWith('app:')) {
      const mappedIcon = customSvgIcons[iconName];

      if (mappedIcon !== undefined) {
        return { icon: mappedIcon };
      }
    }

    // For any other icons, let Quasar handle them with its default behavior
    return undefined;
  };
});
