import { defineStore } from 'pinia';
import { type ComponentPublicInstance } from 'vue';
import type { ItemBlock, Option } from 'src/types/models';
import { useBlockCreatorStore } from './block_creator';

// กำหนดชนิด DOMRefElement สำหรับอ้างอิง DOM หรือ Vue Component
// ใช้สำหรับ scroll/focus block
type DOMRefElement = Element | ComponentPublicInstance | null;

export const useBlockCreatorUIStore = defineStore('blockCreatorUI', () => {
  // Block refs สำหรับ scroll/focus พร้อม cache เพื่อประสิทธิภาพ
  const blockRefsCache = new Map<number, DOMRefElement>();

  // scrollTimeout สำหรับ debounce scroll
  let scrollTimeout: NodeJS.Timeout | null = null;

  // blockRefs proxy สำหรับ access ที่รวดเร็ว
  const blockRefs: Record<number, DOMRefElement> = new Proxy(
    {},
    {
      get(_target, id: string | symbol) {
        // กรณี id เป็น Symbol (ใช้โดย Vue reactivity)
        if (typeof id === 'symbol') {
          return undefined;
        }
        const numId = Number(id);
        // กรณี id ไม่ใช่ตัวเลข
        if (isNaN(numId)) {
          return undefined;
        }
        if (blockRefsCache.has(numId)) {
          return blockRefsCache.get(numId);
        }
        const blockCreatorStore = useBlockCreatorStore();
        const ref = blockCreatorStore.getBlockRef(numId) || null;
        blockRefsCache.set(numId, ref);
        return ref;
      },
      set(_target, id: string | symbol, el) {
        // กรณี id เป็น Symbol (ใช้โดย Vue reactivity)
        if (typeof id === 'symbol') {
          return true;
        }
        const numId = Number(id);
        // กรณี id ไม่ใช่ตัวเลข
        if (isNaN(numId)) {
          return true;
        }
        blockRefsCache.set(numId, el);
        const blockCreatorStore = useBlockCreatorStore();
        blockCreatorStore.setBlockRef(numId, el);
        return true;
      },
    },
  );

  // จัดการ focus FAB event แบบ aggressive block
  const handleFocusFab = (blockId: number) => {
    const blockCreatorStore = useBlockCreatorStore();
    // ขณะสร้าง block ใหม่: ป้องกัน event ทุกอย่าง ยกเว้น target
    if (blockCreatorStore.blockCreationInProgress) {
      if (blockCreatorStore.targetBlockId && blockId !== blockCreatorStore.targetBlockId) {
        // บังคับ FAB กลับไปตำแหน่งที่ถูกต้องทันที
        blockCreatorStore.selectedBlockId = `block-${blockCreatorStore.targetBlockId}`;
        return;
      }
    }
    // ขณะ lock FAB: ไม่อนุญาต reposition ใด ๆ
    if (blockCreatorStore.fabPositionLock) {
      return;
    }
    // อนุญาตเฉพาะ user interaction ที่ถูกต้อง
    blockCreatorStore.setFabPosition(blockId, false);
  };

  // จัดการ question update จาก ItemBlockComponent
  const handleQuestionUpdate = (updateData: {
    questionId?: number;
    questionText?: string;
    itemBlockId: number;
    updatedQuestion?: object;
    updatedBlock?: ItemBlock;
    typeChanged?: boolean;
  }) => {
    const blockCreatorStore = useBlockCreatorStore();
    // กรณีเปลี่ยน type block
    if (updateData.typeChanged && updateData.updatedBlock) {
      // ป้องกัน FAB ก่อน DOM update
      blockCreatorStore.createFabProtection(updateData.itemBlockId);
      // อัปเดต block ใน local store
      const blockIndex = blockCreatorStore.blocks.findIndex(
        (block) => block.id === updateData.itemBlockId,
      );
      if (blockIndex !== -1) {
        blockCreatorStore.blocks[blockIndex] = updateData.updatedBlock;
      }
      // อัปเดต block ใน current assessment ถ้าเป็น evaluate
      if (blockCreatorStore.currentAssessment?.itemBlocks) {
        const assessmentBlockIndex = blockCreatorStore.currentAssessment.itemBlocks.findIndex(
          (block) => block.id === updateData.itemBlockId,
        );
        if (assessmentBlockIndex !== -1) {
          blockCreatorStore.currentAssessment.itemBlocks[assessmentBlockIndex] =
            updateData.updatedBlock;
        }
      }
      return;
    }
    // กรณีอัปเดต question text
    if (updateData.questionId && updateData.questionText !== undefined) {
      // ป้องกัน FAB ก่อน DOM update
      blockCreatorStore.createFabProtection(updateData.itemBlockId);
      // อัปเดต question ใน local store
      const blockIndex = blockCreatorStore.blocks.findIndex(
        (block) => block.id === updateData.itemBlockId,
      );
      if (blockIndex !== -1) {
        const block = blockCreatorStore.blocks[blockIndex];
        if (block?.questions && block.questions.length > 0) {
          const questionIndex = block.questions.findIndex((q) => q.id === updateData.questionId);
          if (questionIndex !== -1 && block.questions[questionIndex]) {
            block.questions[questionIndex].questionText = updateData.questionText;
          }
        }
      }
      // อัปเดต question ใน current assessment ถ้าเป็น evaluate
      if (blockCreatorStore.currentAssessment?.itemBlocks) {
        const assessmentBlockIndex = blockCreatorStore.currentAssessment.itemBlocks.findIndex(
          (block) => block.id === updateData.itemBlockId,
        );
        if (assessmentBlockIndex !== -1) {
          const assessmentBlock =
            blockCreatorStore.currentAssessment.itemBlocks[assessmentBlockIndex];
          if (assessmentBlock?.questions && assessmentBlock.questions.length > 0) {
            const questionIndex = assessmentBlock.questions.findIndex(
              (q) => q.id === updateData.questionId,
            );
            if (questionIndex !== -1 && assessmentBlock.questions[questionIndex]) {
              assessmentBlock.questions[questionIndex].questionText = updateData.questionText;
            }
          }
        }
      }
    }
  };

  // จัดการ option update จาก ItemBlockComponent
  const handleOptionUpdate = (updateData: {
    action: 'created' | 'updated';
    itemBlockId: number;
    option?: Option;
    optionId?: number;
    updateData?: { index: number; option: Option };
  }) => {
    const blockCreatorStore = useBlockCreatorStore();
    // ป้องกัน FAB ก่อน DOM update
    blockCreatorStore.createFabProtection(updateData.itemBlockId);
    // หา block ใน local store
    const blockIndex = blockCreatorStore.blocks.findIndex(
      (block) => block.id === updateData.itemBlockId,
    );
    if (blockIndex === -1) {
      console.error('❌ Block not found for option update:', updateData.itemBlockId);
      return;
    }
    const block = blockCreatorStore.blocks[blockIndex];
    if (!block) {
      console.error('❌ Block is undefined:', updateData.itemBlockId);
      return;
    }
    // กรณีสร้าง option ใหม่
    if (updateData.action === 'created' && updateData.option) {
      if (!block.options) {
        block.options = [];
      }
      block.options.push(updateData.option);
      // อัปเดตใน current assessment ถ้าเป็น evaluate
      if (blockCreatorStore.currentAssessment?.itemBlocks) {
        const assessmentBlockIndex = blockCreatorStore.currentAssessment.itemBlocks.findIndex(
          (assessmentBlock) => assessmentBlock.id === updateData.itemBlockId,
        );
        if (assessmentBlockIndex !== -1) {
          const assessmentBlock =
            blockCreatorStore.currentAssessment.itemBlocks[assessmentBlockIndex];
          if (assessmentBlock) {
            if (!assessmentBlock.options) {
              assessmentBlock.options = [];
            }
            assessmentBlock.options.push(updateData.option);
          }
        }
      }
    }
    // กรณีอัปเดต option
    if (updateData.action === 'updated' && updateData.optionId && updateData.updateData) {
      const { index, option } = updateData.updateData;
      if (block.options && block.options[index]) {
        block.options[index] = option;
      }
      // อัปเดตใน current assessment ถ้าเป็น evaluate
      if (blockCreatorStore.currentAssessment?.itemBlocks) {
        const assessmentBlockIndex = blockCreatorStore.currentAssessment.itemBlocks.findIndex(
          (assessmentBlock) => assessmentBlock.id === updateData.itemBlockId,
        );
        if (assessmentBlockIndex !== -1) {
          const assessmentBlock =
            blockCreatorStore.currentAssessment.itemBlocks[assessmentBlockIndex];
          if (assessmentBlock?.options && assessmentBlock.options[index]) {
            assessmentBlock.options[index] = option;
          }
        }
      }
    }
  };

  // จัดการ isRequired update จาก ItemBlockComponent
  const handleIsRequiredUpdate = async (updateData: {
    itemBlockId: number;
    isRequired: boolean;
  }) => {
    const blockCreatorStore = useBlockCreatorStore();
    // ป้องกัน FAB ก่อน DOM update
    blockCreatorStore.createFabProtection(updateData.itemBlockId);
    // หา block ใน local store
    const blockIndex = blockCreatorStore.blocks.findIndex(
      (block) => block.id === updateData.itemBlockId,
    );
    if (blockIndex === -1) {
      console.error('❌ Block not found for isRequired update:', updateData.itemBlockId);
      return;
    }
    const block = blockCreatorStore.blocks[blockIndex];
    if (!block) {
      console.error('❌ Block is undefined:', updateData.itemBlockId);
      return;
    }
    try {
      // อัปเดต isRequired ใน local store ก่อน (optimistic update)
      const updatedBlock = {
        ...block,
        isRequired: Boolean(updateData.isRequired),
      };
      blockCreatorStore.updateBlock(updatedBlock, blockIndex);
      // อัปเดตใน current assessment ถ้าเป็น evaluate
      if (blockCreatorStore.currentAssessment?.itemBlocks) {
        const assessmentBlockIndex = blockCreatorStore.currentAssessment.itemBlocks.findIndex(
          (assessmentBlock) => assessmentBlock.id === updateData.itemBlockId,
        );
        if (assessmentBlockIndex !== -1) {
          const assessmentBlock =
            blockCreatorStore.currentAssessment.itemBlocks[assessmentBlockIndex];
          if (assessmentBlock) {
            assessmentBlock.isRequired = Boolean(updateData.isRequired);
            // Trigger reactivity (สร้าง array ใหม่เพื่อให้ watcher ทำงาน)
            blockCreatorStore.currentAssessment.itemBlocks = [
              ...blockCreatorStore.currentAssessment.itemBlocks,
            ];
          }
        }
      }
      // เรียก backend API เพื่อบันทึกการเปลี่ยนแปลง
      const assessmentService = new (
        await import('src/services/asm/assessmentService')
      ).AssessmentService('evaluate');
      const apiUpdatedBlock = await assessmentService.updateBlock(updatedBlock);
      if (apiUpdatedBlock) {
        // อัปเดต local store ด้วยข้อมูลจาก API (กรณี backend เปลี่ยนแปลงข้อมูล)
        blockCreatorStore.updateBlock(apiUpdatedBlock, blockIndex);
        // อัปเดตใน current assessment ด้วยข้อมูลจาก API
        if (blockCreatorStore.currentAssessment?.itemBlocks) {
          const assessmentBlockIndex = blockCreatorStore.currentAssessment.itemBlocks.findIndex(
            (assessmentBlock) => assessmentBlock.id === updateData.itemBlockId,
          );
          if (assessmentBlockIndex !== -1) {
            blockCreatorStore.currentAssessment.itemBlocks[assessmentBlockIndex] = apiUpdatedBlock;
            // Trigger reactivity
            blockCreatorStore.currentAssessment.itemBlocks = [
              ...blockCreatorStore.currentAssessment.itemBlocks,
            ];
          }
        }
      }
    } catch (error) {
      console.error(`❌ Failed to update isRequired for block ${updateData.itemBlockId}:`, error);
      // กรณี error: ย้อนกลับค่าเดิม (revert optimistic update)
      blockCreatorStore.updateBlock(block, blockIndex);
      if (blockCreatorStore.currentAssessment?.itemBlocks) {
        const assessmentBlockIndex = blockCreatorStore.currentAssessment.itemBlocks.findIndex(
          (assessmentBlock) => assessmentBlock.id === updateData.itemBlockId,
        );
        if (assessmentBlockIndex !== -1) {
          const assessmentBlock =
            blockCreatorStore.currentAssessment.itemBlocks[assessmentBlockIndex];
          if (assessmentBlock) {
            assessmentBlock.isRequired = block.isRequired;
            // Trigger reactivity
            blockCreatorStore.currentAssessment.itemBlocks = [
              ...blockCreatorStore.currentAssessment.itemBlocks,
            ];
          }
        }
      }
      // ไม่แสดง error notification
    }
  };

  // ฟังก์ชัน cleanup สำหรับ clear timeout และ cache
  const cleanup = () => {
    const blockCreatorStore = useBlockCreatorStore();
    blockCreatorStore.timeoutManager.clearAllTimeouts();
    if (scrollTimeout) {
      clearTimeout(scrollTimeout);
      scrollTimeout = null;
    }
    // ล้าง block refs cache
    blockRefsCache.clear();
  };

  return {
    // จัดการ block refs
    blockRefs,
    blockRefsCache,
    // event handler ต่าง ๆ
    handleFocusFab,
    handleQuestionUpdate,
    handleOptionUpdate,
    handleIsRequiredUpdate,
    // utility
    cleanup,
  };
});
