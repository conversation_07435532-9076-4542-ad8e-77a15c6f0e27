// src/types/quiz/quiz.ts
// ! need to check/refactor this file

import type { Assessment } from './models';

export interface ChoiceSummaryDto {
  choiceId: number;
  choiceText: string;
  isCorrectOption?: boolean;
  selectionCount: number;
  selectionPercentage: number;
}

export enum QuestionType {
  RADIO = 'RADIO',
  CHECKBOX = 'CHECKBOX',
  TEXTFIELD = 'TEXTFIELD',
}

export interface QuizAnswerSummaryResponseDto {
  quizId: number;
  questions: Array<{
    questionId: number;
    questionText: string;
    questionType: string;
    totalResponses: number;
    choicesSummary?: ChoiceSummaryDto[];
    inputAnswersSummary?: Array<{
      inputText: string;
      isCorrectMatch?: boolean;
      count: number;
      percentage: number;
    }>;
  }>;
}
export interface QuizMetaResponse {
  assessmentName: string;
  uniqueUsers: number;
  highestScore: number;
  lowestScore: number;
}
export interface QuizAllResponsesItem {
  questionId: string | number;
  questionText: string;
  chartType: 'pie' | 'bar';
  choices: Array<{
    choiceText: string;
    count: number;
    // other properties
  }>;
  // ... any other structure your /quiz/:id/response returns
}
export type QuizAllResponsesData = QuizAllResponsesItem[];
export interface MappedQuizSummary {
  quizId: number | null;
  quizTitle: string; // จะมาจาก metaData.assessmentName
  numberOfAttempts: number; // จะมาจาก metaData.uniqueUsers
  highestScore: number; // จะมาจาก metaData.highestScore
  lowestScore: number; // จะมาจาก metaData.lowestScore
}

export interface StartQuizRequest {
  linkUrl: string;
  userId: number;
}

export interface SaveQuizResponseRequest {
  submissionId: number;
  questionId: number;
  responseId?: number | undefined;
  selectedOptionId?: number | undefined;
  answerText?: string | undefined;
}

export interface QuizScore {
  score: number;
  totalScore: number;
  isPassed: boolean;
  usedTime: number;
  timeLimit: number;
}

export interface UserSubmission {
  submissionId: number;
  submissionDate: Date;
  finalScore: number;
  totalScore: number;
  passed: boolean;
  timeSpent: string;
}

export interface QuizHeaderWithSubmissions {
  assessment: Assessment;
  totalScore: number;
  userSubmissions: UserSubmission[];
}
