import umsFacPersonRoutes from './umsFacPersonRoutes.ts.bak';
import umsFacRoutes from './umsFacRoutes.ts.bak';
import umsPermRoutes from './umsPermRoutes.ts.bak';

const routes = [
  {
    path: '/ums/ums-test1',
    name: 'ums_test1',
    components: {
      default: () => import('src/pages/ums/ums_test1/UmsTest1View.vue'),
      menu: () => import('src/components/ums/MainMenu.vue'),
    },
    meta: {
      layout: 'MainLayout',
      ums: true,
      perms: [1, 2],
    },
  },
  ...umsPermRoutes,
  ...umsFacRoutes,
  ...umsFacPersonRoutes,
];

export default routes;
