import type { RouteRecordRaw } from 'vue-router';

const idpRoutes: RouteRecordRaw[] = [
  {
    path: '/idp',
    name: 'idp',
    component: () => import('../layouts/MainLayout.vue'),
    beforeEnter: (to, from, next) => {
      if (to.path === '/idp') {
        next({ name: 'idp-management' });
      } else {
        next();
      }
    },
    children: [
      {
        path: 'management',
        name: 'idp-management',
        component: () => import('../pages/idp/IdpManagementPage.vue'),
      },
    ],
  },
];

export default idpRoutes;
