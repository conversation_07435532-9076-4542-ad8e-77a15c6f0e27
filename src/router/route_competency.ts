import type { RouteRecordRaw } from 'vue-router';

const competencyRoutes: RouteRecordRaw[] = [
  {
    path: '/competency',
    name: 'competency',
    component: () => import('../layouts/MainLayout.vue'),
    beforeEnter: (to, from, next) => {
      if (to.path === '/competency') {
        next({ name: 'competency-management' });
      } else {
        next();
      }
    },
    children: [
      {
        path: 'management',
        name: 'competency-management',
        component: () => import('../pages/competency/CompetencyManagementPage.vue'),
      },
    ],
  },
];

export default competencyRoutes;
