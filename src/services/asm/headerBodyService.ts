import { api } from 'src/boot/axios';
import type { HeaderBody } from 'src/types/models';

export class HeaderBodyService {
  private path = '/evaluate/header-bodies';

  async createHeaderBody(itemBlockId: number): Promise<HeaderBody> {
    try {
      const response = await api.post<HeaderBody>(`${this.path}/`, {
        itemBlockId,
        title: 'แบบฟอร์มไม่มีชื่อ',
        description: 'คำอธิบาย',
      });
      return response.data;
    } catch {
      throw new Error('Create header body failed');
    }
  }

  async findAll(): Promise<HeaderBody[]> {
    try {
      const response = await api.get<HeaderBody[]>(`${this.path}/`);
      return response.data;
    } catch {
      throw new Error('Fetch header bodies failed');
    }
  }

  async findOne(id: number): Promise<HeaderBody> {
    try {
      const response = await api.get<HeaderBody>(`${this.path}/${id}`);
      return response.data;
    } catch {
      throw new Error('Fetch header body failed');
    }
  }

  async update(id: number, params: Partial<HeaderBody>): Promise<HeaderBody> {
    try {
      const response = await api.patch<HeaderBody>(`${this.path}/${id}`, params);
      return response.data;
    } catch {
      throw new Error('Update header body failed');
    }
  }

  async remove(id: number): Promise<void> {
    try {
      await api.delete(`${this.path}/${id}`);
    } catch {
      throw new Error('Remove header body failed');
    }
  }
}
