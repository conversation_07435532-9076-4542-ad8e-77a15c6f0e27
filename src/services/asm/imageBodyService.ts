import { api } from 'src/boot/axios';
import { Notify } from 'quasar';
import type { ImageBody } from 'src/types/models';

const showError = (message: string) => {
  Notify.create({
    message,
    type: 'negative',
    position: 'bottom',
    timeout: 3000,
  });
};

export class ImageBodyService {
  private path = 'image-bodies';

  async createImageBody(params: Partial<ImageBody>, file: File): Promise<ImageBody> {
    try {
      const formData = this.toFormData(params, file);
      const response = await api.post<ImageBody>(`${this.path}`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
      return response.data;
    } catch {
      showError('ไม่สามารถเพิ่มรูปภาพได้');
      throw new Error('Create image body failed');
    }
  }

  async getAllImageBodies(): Promise<ImageBody[]> {
    try {
      const response = await api.get<ImageBody[]>(`${this.path}`);
      return response.data;
    } catch {
      showError('ไม่สามารถดึงรายการรูปภาพทั้งหมดได้');
      throw new Error('Fetch image bodies failed');
    }
  }

  async getImageBodyById(id: number): Promise<ImageBody> {
    try {
      const response = await api.get<ImageBody>(`${this.path}/${id}`);
      return response.data;
    } catch {
      showError('ไม่สามารถดึงข้อมูลรูปภาพตาม ID ได้');
      throw new Error('Fetch image body failed');
    }
  }

  async getImageBodyByItemBlockId(itemBlockId: number): Promise<ImageBody | null> {
    try {
      const response = await api.get<ImageBody[]>(`${this.path}`);
      const imageBody = response.data.find((body) => body.itemBlockId === itemBlockId);
      return imageBody || null;
    } catch {
      showError('ไม่สามารถดึงข้อมูลรูปภาพจาก itemBlockId ได้');
      throw new Error('Fetch image body by itemBlockId failed');
    }
  }

  async updateImageBody(id: number, params: Partial<ImageBody>, file?: File): Promise<ImageBody> {
    try {
      const formData = this.toFormData(params, file);
      const response = await api.patch<ImageBody>(`${this.path}/${id}`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
      return response.data;
    } catch {
      showError('ไม่สามารถอัปเดตรูปภาพได้');
      throw new Error('Update image body failed');
    }
  }

  async updateImageTextOnly(
    id: number,
    imageText: string,
    existingImagePath?: string,
    existingImageWidth?: number,
    existingImageHeight?: number,
  ): Promise<ImageBody> {
    try {
      const extractRelativePath = (path: string | null | undefined): string | null => {
        if (!path) return null;
        if (path.startsWith('uploaded_files/') && !path.includes('?') && !path.includes('http')) {
          return path;
        }
        if (path.includes('uploaded_files/')) {
          const match = path.match(/uploaded_files\/[^?]+/);
          return match ? match[0] : null;
        }
        return path;
      };

      const relativeImagePath = extractRelativePath(existingImagePath);

      const formData = new FormData();
      formData.append('imageText', imageText);
      formData.append('imagePath', relativeImagePath || '');
      if (existingImageWidth !== undefined && existingImageWidth !== null) {
        formData.append('imageWidth', JSON.stringify(existingImageWidth));
      }
      if (existingImageHeight !== undefined && existingImageHeight !== null) {
        formData.append('imageHeight', JSON.stringify(existingImageHeight));
      }

      const response = await api.patch<ImageBody>(`${this.path}/${id}`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });

      return response.data;
    } catch (error) {
      showError('ไม่สามารถอัปเดตข้อความรูปภาพได้');
      console.error('❌ updateImageTextOnly failed:', error);
      throw new Error('Update image text failed');
    }
  }

  async removeImageBody(id: number): Promise<void> {
    try {
      await api.delete(`${this.path}/${id}`);
    } catch {
      showError('ไม่สามารถลบรูปภาพได้');
      throw new Error('Remove image body failed');
    }
  }

  private toFormData(data: Record<string, unknown>, file?: File): FormData {
    const formData = new FormData();

    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (['imageWidth', 'imageHeight', 'itemBlockId'].includes(key)) {
          formData.append(key, JSON.stringify(value));
        } else if (key === 'imageText') {
          formData.append('imageText', value as string);
        } else if (key === 'imagePath' && typeof value === 'string') {
          formData.append('imagePath', value);
        }
      }
    });

    if (file) {
      formData.append('imagePath', file);
    }

    return formData;
  }
}
