import { api as axios } from 'src/boot/axios';
import { Notify } from 'quasar';
import type { Submission } from 'src/types/models';

const showError = (message: string) => {
  Notify.create({
    type: 'negative',
    message,
    position: 'bottom',
    timeout: 3000,
  });
};

export class SubmissionService {
  private path = '/submissions';

  async getByAssessmentId(assessmentId: number): Promise<Submission> {
    try {
      const res = await axios.get<Submission>(`${this.path}/${assessmentId}`);
      return res.data;
    } catch {
      showError('ดึงข้อมูลสรุปล้มเหลว');
      throw new Error('Get summary by assessmentId failed');
    }
  }

  async getDraft(assessmentId: number, userId: number): Promise<Submission> {
    try {
      const res = await axios.get<Submission>(`${this.path}/${assessmentId}/${userId}`);
      return res.data;
    } catch {
      showError('ดึงข้อมูลสรุปล้มเหลว');
      throw new Error('Get summary by assessmentId failed');
    }
  }

  async getAll(): Promise<Submission[]> {
    try {
      const res = await axios.get<Submission[]>(this.path);
      return res.data;
    } catch {
      showError('ดึงข้อมูลสรุปทั้งหมดล้มเหลว');
      throw new Error('Get all summaries failed');
    }
  }

  async fecthOne(id: number): Promise<Submission> {
    try {
      const res = await axios.get<Submission>(`${this.path}/${id}`);
      return res.data;
    } catch {
      showError('ดึงข้อมูลสรุปทั้งหมดล้มเหลว');
      throw new Error('Get all summaries failed');
    }
  }

  async create(data: Submission): Promise<Submission> {
    try {
      const payload = {
        ...data,
        endAt: '',
      };
      const res = await axios.post<Submission>(this.path, payload);
      return res.data;
    } catch {
      showError('มี Draft');
      throw new Error('Create summary failed');
    }
  }

  async update(submissionId: number) {
    try {
      const res = await axios.patch<Submission>(`${this.path}/submit-assessment/${submissionId}`);
      return res.data;
    } catch {
      showError('อัปเดตรายงานสรุปล้มเหลว');
      throw new Error('Update summary failed');
    }
  }

  async remove(id: number): Promise<void> {
    try {
      await axios.delete(`${this.path}/${id}`);
    } catch {
      showError('ลบรายงานสรุปล้มเหลว');
      throw new Error('Remove summary failed');
    }
  }

  async startSubmission(linkUrl: string, userId: number): Promise<Submission> {
    try {
      const res = await axios.post<Submission>(`${this.path}/start-assessment`, {
        linkUrl,
        userId,
      });
      return res.data;
    } catch {
      showError('ไม่สามารถเริ่มการส่งทำแบบฟอร์มได้');
      throw new Error('Start submission failed');
    }
  }
}

export const summaryService = new SubmissionService();
