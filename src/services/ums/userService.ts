import type { QTableProps } from 'quasar';
import { api } from 'src/boot/axios';
import type { DataResponse } from 'src/types/data';
import type { User } from 'src/types/models';
import { formatParams } from 'src/utils/utils';

export class UserService {
  private static path = 'users';

  static createUser(dto: User) {
    const { roleId, roles, ...restData } = dto;
    const res = api.post<User>(`${this.path}`, {
      ...restData,
      roles: roles,
      roleIds: roleId, // ส่ง roleId array
    });
    return res;
  }

  static getUserById(id: number) {
    return api.get<User>(`${this.path}/${id}`);
  }

  static updateUser(id: number, data: Partial<User>) {
    const { roleId, roles, ...restData } = data;

    const res = api.patch<User>(`${this.path}/${id}`, {
      ...restData,
      roles: roles,
      roleIds: roleId, // ส่ง roleId array
    });

    return res;
  }

  static deleteUser(id: number) {
    return api.delete<User>(`${this.path}/${id}`);
  }

  static getUsers(pagination: QTableProps['pagination']) {
    const params = formatParams(pagination);
    return api.get<DataResponse<User>>(`${this.path}`, { params });
  }
}
