<template>
  <div style="position: fixed; top: 100px; right: 64px; width: auto; height: auto; z-index: 2000">
    <q-btn
      class="float-btn-toggle"
      icon="app:quiz"
      size="1.2rem"
      color="primary"
      round
      @click="show = !show"
      @mousedown="startDrag"
      :style="{
        position: 'absolute',
        top: `${btnPosition.top}px`,
        right: `${btnPosition.right}px`,
        zIndex: 2001,
      }"
    >
      <!-- <img src="/svg/quiz.svg" alt="quiz" style="width: 32px; height: 32px" /> -->
    </q-btn>
    <transition name="fade">
      <div
        v-if="show"
        class="float-btn-floating"
        ref="floatBtnRef"
        :style="{
          position: 'absolute',
          top: `${btnPosition.top + btnSize + 8}px`,
          right: `${btnPosition.right}px`,
          zIndex: 2000,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }"
      >
        <div
          class="question-grid"
          :style="{
            gridTemplateColumns: `repeat(${gridColumns}, 1fr)`,
            gridTemplateRows: `repeat(${gridRows}, 1fr)`,
            width:
              gridColumns < maxColumns ? `${gridColumns * 62 + (gridColumns - 1) * 6}px` : '100%',
            justifyContent: gridColumns < maxColumns ? 'center' : 'unset',
          }"
        >
          <q-btn
            v-for="n in questionNumbers"
            :key="n"
            :label="n.toString()"
            :color="
              doneList && doneList[n - 1]
                ? 'positive'
                : n === currentQuestion
                  ? 'primary'
                  : 'grey-8'
            "
            class="question-btn"
            :outline="!(doneList && doneList[n - 1]) && n !== currentQuestion"
            :unelevated="n === currentQuestion || (doneList && doneList[n - 1])"
            @click="() => emit('select', n)"
          />
        </div>
        <div
          style="
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 12px auto 0 auto;
          "
        >
          <q-btn
            flat
            dense
            icon="chevron_left"
            :disable="currentPage === 1"
            @click="prevPage"
            style="min-width: 32px"
          />
          <span style="margin: 0 12px; font-weight: bold">
            {{ currentPage }} / {{ totalPages }}
          </span>
          <q-btn
            flat
            dense
            icon="chevron_right"
            :disable="currentPage === totalPages"
            @click="nextPage"
            style="min-width: 32px"
          />
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, watch } from 'vue';

const props = defineProps<{
  totalQuestions: number;
  currentQuestion?: number;
  doneList?: boolean[];
}>();
const emit = defineEmits(['select']);
const show = ref(false);
const floatBtnRef = ref<HTMLElement | null>(null);

// Responsive: ตรวจจับ mobile
const isMobile = ref(false);
function updateIsMobile() {
  isMobile.value = window.innerWidth < 600;
}
onMounted(() => {
  updateIsMobile();
  window.addEventListener('resize', updateIsMobile);
  document.addEventListener('mousedown', handleClickOutside);
});
onBeforeUnmount(() => {
  window.removeEventListener('resize', updateIsMobile);
  document.removeEventListener('mousedown', handleClickOutside);
  document.removeEventListener('mousemove', onDrag);
  document.removeEventListener('mouseup', stopDrag);
});

// ใช้แถวละ 5 ข้อเสมอ
const maxColumns = computed(() => 5);
const gridColumns = computed(() => maxColumns.value);
const QUESTIONS_PER_PAGE = 20;

// ปรับ gridRows ให้เหมาะสมกับจำนวนข้อในแต่ละหน้า
const gridRows = computed(() => {
  return (
    Math.ceil(
      Math.min(
        QUESTIONS_PER_PAGE,
        props.totalQuestions - (currentPage.value - 1) * QUESTIONS_PER_PAGE,
      ) / gridColumns.value,
    ) || 1
  );
});

// Pagination logic (ทุกขนาดหน้าจอ)
const currentPage = ref(1);
const totalPages = computed(() => Math.ceil(props.totalQuestions / QUESTIONS_PER_PAGE) || 1);

const currentPageQuestions = computed(() => {
  const start = (currentPage.value - 1) * QUESTIONS_PER_PAGE + 1;
  const end = Math.min(start + QUESTIONS_PER_PAGE - 1, props.totalQuestions);
  return Array.from({ length: end - start + 1 }, (_, i) => start + i);
});

// ใช้ questions เฉพาะหน้าปัจจุบันเสมอ
const questionNumbers = computed(() => currentPageQuestions.value);

function prevPage() {
  if (currentPage.value > 1) currentPage.value--;
}
function nextPage() {
  if (currentPage.value < totalPages.value) currentPage.value++;
}

// เมื่อเปลี่ยนข้อ ให้เปลี่ยนหน้าอัตโนมัติถ้าข้ออยู่นอกหน้าปัจจุบัน
watch(
  () => props.currentQuestion,
  (newVal) => {
    if (!newVal) return;
    const page = Math.ceil(newVal / QUESTIONS_PER_PAGE);
    if (page !== currentPage.value) currentPage.value = page;
  },
);

function handleClickOutside(event: MouseEvent) {
  if (!show.value) return;
  const el = floatBtnRef.value;
  if (el && el.contains(event.target as Node)) return;
  show.value = false;
}

// เปลี่ยน defaultTop และ defaultRight เป็น 0 เพื่อให้ปุ่มอยู่ขวาบนสุด
const defaultTop = 0;
const defaultRight = 0;
const btnPosition = ref({ top: defaultTop, right: defaultRight });
const btnSize = 54; // ปรับขนาดปุ่มให้ตรงกับ style (width/height 54px)

let dragging = false;
let dragStart = { x: 0, y: 0 };
let btnStart = { top: defaultTop, right: defaultRight };

function startDrag(e: MouseEvent) {
  dragging = true;
  dragStart = { x: e.clientX, y: e.clientY };
  btnStart = { ...btnPosition.value };
  document.addEventListener('mousemove', onDrag);
  document.addEventListener('mouseup', stopDrag);
}

function onDrag(e: MouseEvent) {
  if (!dragging) return;
  const dx = e.clientX - dragStart.x;
  const dy = e.clientY - dragStart.y;
  // ปรับ top/right ตามการลาก
  btnPosition.value.top = Math.max(0, btnStart.top + dy);
  btnPosition.value.right = Math.max(0, btnStart.right - dx);
}

function stopDrag() {
  dragging = false;
  document.removeEventListener('mousemove', onDrag);
  document.removeEventListener('mouseup', stopDrag);
}

onMounted(() => {
  document.addEventListener('mousedown', handleClickOutside);
});
onBeforeUnmount(() => {
  document.removeEventListener('mousedown', handleClickOutside);
  document.removeEventListener('mousemove', onDrag);
  document.removeEventListener('mouseup', stopDrag);
});
</script>

<style scoped>
.float-btn-floating {
  /* position, top, right moved to inline style */
  background: #fff;
  border-radius: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.18);
  padding: 16px 6px 8px 16px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  transition: box-shadow 0.2s;
  max-width: 578px;
  max-height: 408px;
}
.question-grid {
  display: grid;
  gap: 6px;
  margin: 0 auto;
  max-width: 546px;
  max-height: 250px;
  overflow-x: hidden;
  overflow-y: auto;
  justify-content: center;
  padding-right: 4px;
  padding-bottom: 16px;
  padding-top: 4px;
}
.question-btn {
  width: 100%;
  height: 100%;
  min-width: 35px;
  min-height: 44px;
  font-weight: bold;
  font-size: 1.1em;
  padding: 0;
  transition: background 0.15s;
}
.float-btn-toggle {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.18);
  position: absolute;
  /* top: 0;
  right: 0; */
  z-index: 2001;
  width: 54px;
  height: 54px;
  font-size: 1.5em;
  border-radius: 100px;
  cursor: grab;
}
.float-btn-toggle:active {
  cursor: grabbing;
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
