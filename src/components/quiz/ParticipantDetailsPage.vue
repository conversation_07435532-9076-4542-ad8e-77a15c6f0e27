<!-- eslint-disable @typescript-eslint/no-explicit-any -->
<template>
  <q-page class="bg-page-primary q-pa-md">
    <div class="container">
      <!-- Header -->
      <q-card class="q-mb-lg">
        <q-card-section class="row items-center">
          <q-btn
            flat
            round
            dense
            icon="arrow_back"
            @click="goBack"
            color="accent"
            size="md"
            class="q-mr-md"
          >
            <q-tooltip>กลับไปหน้าก่อน</q-tooltip>
          </q-btn>
          <div class="col">
            <div class="header text-weight-bold text-accent">
              <q-icon name="assignment" color="accent" class="q-mr-sm" /> รายละเอียดผู้เข้าสอบ
            </div>
            <div class="sub-body text-grey-7">ข้อมูลและผลการทดสอบ</div>
          </div>

          <!-- Admin Toggle Button -->
          <div class="row items-center q-gutter-sm">
            <q-toggle
              v-model="isTextFieldOnly"
              checked-icon="grade"
              unchecked-icon="list"
              :label="isTextFieldOnly ? 'ให้คะแนน' : 'แสดงทุกข้อ'"
              color="secondary"
              size="lg"
              @update:model-value="handleToggleChange"
            />
          </div>
        </q-card-section>
      </q-card>

      <!-- Loading State -->
      <q-card v-if="dashboardStore.isLoadingParticipantDetails" class="q-mb-lg">
        <q-card-section class="text-center q-py-xl">
          <q-spinner-dots size="3em" color="accent" />
          <div class="body q-mt-md text-grey-8">กำลังโหลดข้อมูล...</div>
        </q-card-section>
      </q-card>

      <!-- Error State -->
      <q-banner
        v-if="dashboardStore.error && !dashboardStore.isLoadingParticipantDetails"
        class="bg-negative text-white q-mb-lg"
        rounded
      >
        <template v-slot:avatar>
          <q-icon name="error" />
        </template>
        <div>
          <div class="text-weight-bold">เกิดข้อผิดพลาด!</div>
          <div>{{ dashboardStore.error }}</div>
        </div>
        <template v-slot:action>
          <q-btn flat color="white" label="ลองใหม่" @click="retryFetch">
            <q-icon name="refresh" class="q-ml-sm" />
          </q-btn>
          <q-btn flat color="white" label="ล้าง" @click="dashboardStore.clearError">
            <q-icon name="clear" class="q-ml-sm" />
          </q-btn>
        </template>
      </q-banner>

      <!-- Participant Details Content -->
      <div v-if="dashboardStore.participantDetails && !dashboardStore.isLoadingParticipantDetails">
        <!-- Participant Summary Card -->
        <q-card class="q-mb-lg">
          <q-card-section>
            <div class="header text-weight-bold text-accent q-mb-md">
              <q-icon name="person" color="accent" class="q-mr-sm" />
              ข้อมูลสรุป
              <q-chip
                v-if="isTextFieldOnly"
                color="secondary"
                text-color="white"
                size="sm"
                class="q-ml-sm"
                label="โหมดให้คะแนน"
                icon="grade"
              />
            </div>

            <div class="row q-col-gutter-md">
              <div class="col-12 col-md-6">
                <q-item>
                  <q-item-section avatar>
                    <q-icon name="badge" color="accent" />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label class="sub-body text-grey-7">ชื่อผู้เข้าสอบ</q-item-label>
                    <q-item-label class="body text-weight-bold text-grey-9">
                      {{ participantData?.userName }}
                    </q-item-label>
                  </q-item-section>
                </q-item>
              </div>

              <div class="col-12 col-md-6">
                <q-item>
                  <q-item-section avatar>
                    <q-icon name="description" color="info" />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label class="sub-body text-grey-7">แบบประเมิน</q-item-label>
                    <q-item-label class="body text-weight-bold text-grey-9">
                      {{ participantData?.assessmentName }}
                    </q-item-label>
                  </q-item-section>
                </q-item>
              </div>

              <!-- แสดงข้อมูลคะแนนและเวลาเฉพาะในโหมดปกติ -->
              <template v-if="!isTextFieldOnly">
                <div class="col-12 col-md-6">
                  <q-item>
                    <q-item-section avatar>
                      <q-icon name="grade" color="warning" />
                    </q-item-section>
                    <q-item-section>
                      <q-item-label class="sub-body text-grey-7">คะแนนรวม</q-item-label>
                      <q-item-label class="body text-weight-bold text-grey-9">
                        {{ participantData?.totalScore }}/{{ participantData?.maxScore }}
                        <span class="text-grey-6">
                          ({{ Number(participantData?.scorePercentage).toFixed(2) }}%)
                        </span>
                      </q-item-label>
                    </q-item-section>
                  </q-item>
                </div>

                <div class="col-12 col-md-6">
                  <q-item>
                    <q-item-section avatar>
                      <q-icon name="schedule" color="info" />
                    </q-item-section>
                    <q-item-section>
                      <q-item-label class="sub-body text-grey-7">เวลาเริ่ม</q-item-label>
                      <q-item-label class="body text-grey-9">
                        {{ formatDateTime(participantData?.startTime) }}
                      </q-item-label>
                    </q-item-section>
                  </q-item>
                </div>

                <div class="col-12 col-md-6">
                  <q-item>
                    <q-item-section avatar>
                      <q-icon name="done_all" color="positive" />
                    </q-item-section>
                    <q-item-section>
                      <q-item-label class="sub-body text-grey-7">เวลาสิ้นสุด</q-item-label>
                      <q-item-label class="body text-grey-9">
                        {{ formatDateTime(participantData?.endTime) }}
                      </q-item-label>
                    </q-item-section>
                  </q-item>
                </div>
              </template>

              <!-- แสดงข้อมูลจำนวนคำถาม TEXTFIELD ในโหมดให้คะแนน -->
              <template v-else>
                <div class="col-12 col-md-6">
                  <q-item>
                    <q-item-section avatar>
                      <q-icon name="edit" color="secondary" />
                    </q-item-section>
                    <q-item-section>
                      <q-item-label class="sub-body text-grey-7">คำถามที่ต้องให้คะแนน</q-item-label>
                      <q-item-label class="body text-weight-bold text-grey-9">
                        {{ dashboardStore.participantDetails?.total || 0 }} คำถาม
                      </q-item-label>
                    </q-item-section>
                  </q-item>
                </div>
              </template>
            </div>
          </q-card-section>
        </q-card>

        <!-- Questions and Answers -->
        <q-card class="q-mb-lg">
          <q-card-section>
            <div class="header text-weight-bold text-accent q-mb-md q-pb-md">
              <q-icon name="description" color="accent" class="q-mr-sm" />
              รายละเอียดคำตอบ
            </div>

            <div class="q-gutter-md">
              <q-card
                v-for="question in paginatedQuestions"
                :key="question.questionId"
                bordered
                flat
              >
                <q-card-section>
                  <div class="row justify-between items-start q-mb-md">
                    <div class="row items-center">
                      <q-chip
                        color="primary"
                        text-color="dark"
                        :label="question.questionSequence"
                        square
                      >
                      </q-chip>
                      <div class="body text-weight-medium text-grey-9 q-ml-md">
                        {{ question.questionText }}
                      </div>
                    </div>
                    <div class="row q-gutter-sm">
                      <q-chip
                        color="secondary"
                        text-color="white"
                        :label="`${question.score} คะแนน`"
                        icon="star"
                        square
                      />
                      <!-- Only show correct/incorrect chip for non-TEXTFIELD questions -->
                      <q-chip
                        v-if="question.questionType !== 'TEXTFIELD'"
                        :color="question.isCorrect ? 'positive' : 'negative'"
                        text-color="white"
                        :label="question.isCorrect ? 'ถูกต้อง' : 'ผิด'"
                        :icon="question.isCorrect ? 'check_circle' : 'cancel'"
                        square
                      />
                      <!-- For TEXTFIELD questions, show answered status -->
                      <q-chip
                        v-else-if="question.questionType === 'TEXTFIELD'"
                        color="info"
                        text-color="white"
                        :label="question.textAnswer ? 'ตอบแล้ว' : 'ไม่ได้ตอบ'"
                        :icon="question.textAnswer ? 'edit' : 'edit_off'"
                        square
                      />
                    </div>
                  </div>

                  <div v-if="question.selectedOptionText || question.textAnswer" class="q-mb-md">
                    <q-separator class="q-mb-md" />
                  </div>

                  <!-- Display text answer for TEXTFIELD questions -->
                  <div
                    v-if="question.questionType === 'TEXTFIELD' && question.textAnswer"
                    class="q-mb-md"
                  >
                    <div class="body text-weight-medium text-grey-8 q-mb-sm">
                      <q-icon name="edit" color="grey-7" class="q-mr-xs" />
                      คำตอบที่กรอก:
                    </div>
                    <q-card flat bordered class="q-pa-md bg-grey-1">
                      <div class="body text-grey-9">
                        {{ question.textAnswer }}
                      </div>
                    </q-card>

                    <!-- Admin Score Input Section -->
                    <div v-if="isTextFieldOnly" class="q-mt-md">
                      <q-separator class="q-mb-md" />
                      <q-card flat bordered class="q-pa-md bg-grey-1">
                        <div class="row items-center justify-between">
                          <div class="col-auto">
                            <div class="body text-weight-medium text-grey-9 q-mb-sm">
                              <q-icon name="grade" color="warning" class="q-mr-sm" />
                              ให้คะแนน
                            </div>
                          </div>
                          <div class="col-auto">
                            <div class="row items-center q-gutter-md">
                              <q-input
                                v-model.number="customScores[question.questionId]"
                                type="number"
                                min="0"
                                :max="getMaxScore(question)"
                                label="คะแนน"
                                dense
                                outlined
                                class="score-input"
                                :rules="[
                                  (val) => val >= 0 || 'คะแนนต้องไม่น้อยกว่า 0',
                                  (val) =>
                                    val <= getMaxScore(question) ||
                                    `คะแนนต้องไม่เกิน ${getMaxScore(question)}`,
                                ]"
                                @focus="handleScoreEvent(question.questionId, 'focus', $event)"
                                @input="handleScoreEvent(question.questionId, 'input', $event)"
                                @blur="handleScoreEvent(question.questionId, 'blur', $event)"
                              />
                              <div class="sub-body text-grey-6">/ {{ getMaxScore(question) }}</div>
                              <!-- Save status indicator -->
                              <q-icon
                                v-if="saveStatus[question.questionId] === 'saving'"
                                name="hourglass_empty"
                                color="orange"
                                size="sm"
                              >
                                <q-tooltip>กำลังบันทึก...</q-tooltip>
                              </q-icon>
                              <q-icon
                                v-else-if="saveStatus[question.questionId] === 'saved'"
                                name="check_circle"
                                color="positive"
                                size="sm"
                              >
                                <q-tooltip>บันทึกเรียบร้อย</q-tooltip>
                              </q-icon>
                              <q-icon
                                v-else-if="saveStatus[question.questionId] === 'error'"
                                name="error"
                                color="negative"
                                size="sm"
                              >
                                <q-tooltip>เกิดข้อผิดพลาดในการบันทึก</q-tooltip>
                              </q-icon>
                            </div>
                          </div>
                        </div>
                      </q-card>
                    </div>
                  </div>

                  <!-- Display options for multiple choice questions -->
                  <div
                    v-if="
                      question.questionType !== 'TEXTFIELD' &&
                      question.options &&
                      question.options.length > 0
                    "
                  >
                    <div class="body text-weight-medium text-grey-8 q-mb-md">
                      <q-icon name="list" color="grey-7" class="q-mr-xs" />
                      ตัวเลือกทั้งหมด:
                    </div>
                    <div class="q-gutter-sm">
                      <q-item
                        v-for="option in question.options"
                        :key="option.id"
                        class="q-mb-md rounded-borders"
                        :class="getOptionBorderClass(option)"
                        dense
                      >
                        <q-item-section avatar>
                          <div class="flex items-center">
                            <!-- Icon for checkbox questions -->
                            <q-icon
                              v-if="question.questionType === 'CHECKBOX'"
                              :name="option.isSelected ? 'check_box' : 'check_box_outline_blank'"
                              :color="getCheckboxIconColor(option)"
                              size="md"
                            />
                            <!-- Icon for radio questions -->
                            <q-icon
                              v-else-if="option.isSelected && option.value === 1"
                              name="check_circle"
                              color="positive"
                              size="md"
                            />
                            <q-icon
                              v-else-if="!option.isSelected && option.value === 1"
                              name="check_circle_outline"
                              color="positive"
                              size="md"
                            />
                            <q-icon
                              v-else-if="option.isSelected && option.value === 0"
                              name="cancel"
                              color="negative"
                              size="md"
                            />
                            <q-icon v-else name="radio_button_unchecked" color="grey-6" size="md" />
                          </div>
                        </q-item-section>

                        <q-item-section>
                          <div class="row items-center q-gutter-sm">
                            <q-item-label :class="getOptionTextClass(option)" class="body">
                              {{ option.optionText }}
                            </q-item-label>
                            <q-chip
                              v-if="option.isSelected"
                              color="dark"
                              text-color="white"
                              size="sm"
                              dense
                              label="ตัวเลือกที่เลือก"
                              icon="touch_app"
                            />
                          </div>
                        </q-item-section>
                      </q-item>
                    </div>
                  </div>
                </q-card-section>
              </q-card>

              <!-- Pagination Component -->
              <div class="row justify-center q-mt-lg">
                <div class="col-12 text-center q-mb-md">
                  <div class="body text-grey-7">
                    แสดงคำถามที่ {{ (currentPage - 1) * itemsPerPage + 1 }}-{{
                      Math.min(
                        currentPage * itemsPerPage,
                        dashboardStore.participantDetails?.total || 0,
                      )
                    }}
                    จากทั้งหมด {{ dashboardStore.participantDetails?.total || 0 }} คำถาม
                  </div>
                </div>
                <q-pagination
                  v-model="currentPage"
                  :max="totalPages"
                  :max-pages="6"
                  :boundary-links="true"
                  :direction-links="true"
                  color="black"
                  @update:model-value="handlePageChange"
                />
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>

      <!-- No Data State -->
      <q-card
        v-else-if="!dashboardStore.isLoadingParticipantDetails && !dashboardStore.error"
        class="q-mb-lg"
      >
        <q-card-section class="text-center q-py-xl">
          <q-icon name="inbox" size="4em" color="grey-5" />
          <div class="header text-grey-8 q-mt-md">ไม่พบข้อมูล</div>
          <div class="sub-body text-grey-6">ไม่พบข้อมูลผู้เข้าสอบที่คุณต้องการ</div>
        </q-card-section>
      </q-card>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { onMounted, ref, computed, onBeforeUnmount } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useQuizDashboardStore } from 'src/stores/quizdashboardStore';
import type { DataParams } from 'src/types/data';
import type {
  OptionWithUserData,
  ItemBlockWithUserData,
  QuestionWithUserData,
  ProcessedQuestionData,
} from 'src/types/quiz-dashboard';

const route = useRoute();
const router = useRouter();
const dashboardStore = useQuizDashboardStore();

// Get participant data from the first item in the DataResponse
const participantData = computed(() => {
  return dashboardStore.participantDetails?.data?.[0] || null;
});

// Initialize pagination parameters with type safety
const paginationParams = ref<DataParams>({
  page: 1,
  limit: 5,
  sortBy: null,
  order: 'ASC',
  search: null,
});

// State for admin filter
const isTextFieldOnly = ref(false);

// State for custom scoring with improved auto-save capabilities
const customScores = ref<Record<number, number>>({});
const saveStatus = ref<Record<number, 'saving' | 'saved' | 'error' | null>>({});
const saveTimeouts: Record<number, NodeJS.Timeout> = {};
const originalScores = ref<Record<number, number>>({}); // Track original values to detect real changes

// Auto-save configuration following project patterns
const IMMEDIATE_SAVE_ON_BLUR = true; // matches HeaderBlock.vue pattern

// Update currentPage and itemsPerPage to use paginationParams
const currentPage = computed({
  get: () => paginationParams.value.page,
  set: (value) => {
    paginationParams.value.page = value;
  },
});

const itemsPerPage = computed({
  get: () => paginationParams.value.limit,
  set: (value) => {
    paginationParams.value.limit = value;
  },
});

const participantId = Number(route.params.participantId);

// Compute total pages based on backend pagination metadata
const totalPages = computed((): number => {
  if (!dashboardStore.participantDetails) return 1;
  return Math.ceil(dashboardStore.participantDetails.total / itemsPerPage.value);
});

// Get all questions from itemBlocks (flatten structure)
const allQuestions = computed((): Array<ProcessedQuestionData> => {
  if (!participantData.value?.itemBlocks) return [];

  const questions: Array<ProcessedQuestionData> = [];

  (participantData.value.itemBlocks as ItemBlockWithUserData[]).forEach(
    (itemBlock: ItemBlockWithUserData) => {
      if (itemBlock.questions) {
        itemBlock.questions.forEach((question: QuestionWithUserData) => {
          const userResponse = question.userResponse;
          const questionOptions = itemBlock.options || [];

          // สำหรับ TEXTFIELD ให้เก็บคะแนนปัจจุบันที่ได้รับ
          const currentScore =
            itemBlock.type === 'TEXTFIELD' && userResponse ? userResponse.currentScore || 0 : 0;

          questions.push({
            questionId: question.id,
            questionSequence: question.sequence,
            questionText: question.questionText,
            questionType: itemBlock.type,
            score: question.score || 0, // คะแนนเต็มจาก models.ts Question
            isCorrect: userResponse?.isCorrect || false,
            ...(userResponse?.selectedOptionText && {
              selectedOptionText: userResponse.selectedOptionText,
            }),
            ...(userResponse?.textAnswer && { textAnswer: userResponse.textAnswer }),
            ...(itemBlock.type === 'TEXTFIELD' && { currentScore }),
            options: questionOptions,
            itemBlockId: itemBlock.id,
            itemBlockType: itemBlock.type,
          });

          // ตั้งค่าคะแนนเริ่มต้นใน customScores ถ้าเป็น TEXTFIELD
          if (itemBlock.type === 'TEXTFIELD' && currentScore !== undefined) {
            customScores.value[question.id] = currentScore;
          }
        });
      }
    },
  );

  return questions;
});

// Get paginated questions (already handled by backend pagination of itemBlocks)
const paginatedQuestions = computed(() => {
  return allQuestions.value;
});

// Methods
function formatDateTime(dateString: string | undefined): string {
  if (!dateString) return 'N/A';
  try {
    return new Date(dateString).toLocaleString('th-TH', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (e) {
    return dateString;
  }
}

function goBack() {
  router.back();
}

async function retryFetch() {
  dashboardStore.clearError();
  await fetchParticipantDetails();
}

async function fetchParticipantDetails() {
  if (participantId && !isNaN(participantId)) {
    try {
      if (isTextFieldOnly.value) {
        // ใช้ API สำหรับ textfield grading (เฉพาะข้อมูลที่จำเป็นสำหรับการให้คะแนน)
        await dashboardStore.fetchParticipantTextFieldGrading(
          participantId,
          paginationParams.value,
        );
      } else {
        // ใช้ API ปกติ (ข้อมูลครบถ้วนสำหรับการแสดงผล)
        await dashboardStore.fetchParticipantDetails(participantId, paginationParams.value);
      }
    } catch {
      // Error handling will be shown in UI
    }
  }
}

// Toggle handler for admin mode
async function handleToggleChange(value: boolean) {
  isTextFieldOnly.value = value;
  paginationParams.value.page = 1; // Reset to first page

  // Clear existing data to show loading state
  dashboardStore.participantDetails = null;

  // Fetch data using appropriate API based on toggle state
  await fetchParticipantDetails();
}

// Unified event handler for all score input events (focus, input, blur)
// This follows the pattern found in EditorTool.vue and other components in the codebase
// where a single handler manages multiple event types for better maintainability
function handleScoreEvent(questionId: number, eventType: 'focus' | 'input' | 'blur', event: Event) {
  const target = event.target as HTMLInputElement;

  switch (eventType) {
    case 'focus':
      handleScoreFocus(questionId, target);
      break;
    case 'input':
      handleScoreInput(questionId, target);
      break;
    case 'blur':
      handleScoreBlur(questionId);
      break;
  }
}

// Internal focus handler
function handleScoreFocus(questionId: number, target: HTMLInputElement) {
  // Store the original value when user starts editing
  const currentValue = parseFloat(target.value) || 0;
  originalScores.value[questionId] = currentValue;

  // Select text on focus for better UX
  target.select();

  // Clear any existing save status and timeout
  saveStatus.value[questionId] = null;
  if (saveTimeouts[questionId]) {
    clearTimeout(saveTimeouts[questionId]);
    delete saveTimeouts[questionId];
  }
}

// Internal input handler with validation only (no auto-save)
function handleScoreInput(questionId: number, target: HTMLInputElement) {
  let value = parseFloat(target.value);
  const maxScore = getMaxScore(
    allQuestions.value.find((q) => q.questionId === questionId) || { score: 0 },
  );

  // Validate and enforce limits
  if (isNaN(value)) value = 0;
  else if (value < 0) value = 0;
  else if (value > maxScore) value = maxScore;

  // Update input and state
  target.value = value.toString();
  customScores.value[questionId] = value;

  // Clear status
  saveStatus.value[questionId] = null;
}

// Internal blur handler - save only if value actually changed
function handleScoreBlur(questionId: number) {
  const currentScore = customScores.value[questionId];
  const originalScore = originalScores.value[questionId];

  // Validate current score
  if (currentScore === undefined || currentScore === null || isNaN(currentScore)) {
    return;
  }

  // Check if the value actually changed from the original
  const hasChanged = originalScore !== currentScore;

  // Cancel any pending timeouts
  if (saveTimeouts[questionId]) {
    clearTimeout(saveTimeouts[questionId]);
    delete saveTimeouts[questionId];
  }

  // Only save if the value actually changed and immediate save is enabled
  if (hasChanged && IMMEDIATE_SAVE_ON_BLUR) {
    void saveScore(questionId, currentScore);
  }

  // Clean up the original score tracking
  delete originalScores.value[questionId];
}

// Extracted save function with enhanced error handling
async function saveScore(questionId: number, score: number) {
  const maxScore = getMaxScore(
    allQuestions.value.find((q) => q.questionId === questionId) || { score: 0 },
  );

  // Final validation before save
  if (score < 0 || score > maxScore) {
    saveStatus.value[questionId] = 'error';
    return;
  }

  // Set saving status
  saveStatus.value[questionId] = 'saving';

  try {
    // Get submission ID from participant data
    const submissionId = participantData.value?.submissionId;
    if (!submissionId) {
      throw new Error('Submission ID not found');
    }

    // Call API to save custom score
    await dashboardStore.saveCustomScore(submissionId, questionId, score);

    // Set saved status
    saveStatus.value[questionId] = 'saved';

    // Clear saved status after 3 seconds (following project patterns)
    setTimeout(() => {
      if (saveStatus.value[questionId] === 'saved') {
        saveStatus.value[questionId] = null;
      }
    }, 3000);
  } catch {
    saveStatus.value[questionId] = 'error';

    // Clear error status after 5 seconds
    setTimeout(() => {
      if (saveStatus.value[questionId] === 'error') {
        saveStatus.value[questionId] = null;
      }
    }, 5000);

    // Show error feedback to user (following globalStore pattern from other components)
    // You can add a notification here if needed
  }
}

// Helper function to get max score for a question
function getMaxScore(question: { score?: number }): number {
  // ใช้คะแนนเต็มจาก question.score ที่มาจาก models.ts
  return question.score || 0; // Default to 0 if score is not defined
}

function handlePageChange(newPage: number) {
  // เพิ่ม loading state เมื่อเปลี่ยนหน้า
  paginationParams.value.page = newPage;
  window.scrollTo({ top: 0, behavior: 'smooth' });

  // เพิ่ม debouncing เพื่อป้องกันการ call API บ่อยเกินไป
  setTimeout(() => {
    if (paginationParams.value.page === newPage) {
      void fetchParticipantDetails();
    }
  }, 100);
}

// UI helper functions for option styling
function getOptionBorderClass(option: OptionWithUserData): string {
  if (option.isSelected && option.value === 1) {
    // คำตอบที่เลือกและถูกต้อง
    return 'border-positive';
  } else if (option.isSelected && option.value === 0) {
    // คำตอบที่เลือกแต่ผิด
    return 'border-negative';
  } else if (!option.isSelected && option.value === 1) {
    // คำตอบที่ถูกต้องแต่ไม่ได้เลือก
    return 'border-positive';
  }
  return 'border-grey-4';
}

function getCheckboxIconColor(option: OptionWithUserData): string {
  if (option.value === 1) {
    return 'positive';
  } else if (option.isSelected && option.value === 0) {
    return 'negative';
  }
  return 'grey-6';
}

function getOptionTextClass(option: OptionWithUserData): string {
  if (option.value === 1) {
    // คำตอบที่ถูกต้อง (ไม่ว่าจะเลือกหรือไม่)
    return 'text-weight-bold text-positive';
  } else if (option.isSelected && option.value === 0) {
    // คำตอบที่เลือกแต่ผิด
    return 'text-weight-medium text-negative';
  }
  return 'text-grey-8';
}

// Lifecycle
onMounted(async () => {
  // Scroll to top when component mounts
  window.scrollTo({ top: 0, behavior: 'smooth' });
  await fetchParticipantDetails();
});

// Cleanup timeouts when component unmounts
onBeforeUnmount(() => {
  // Clear all pending save timeouts to prevent memory leaks
  Object.values(saveTimeouts).forEach((timeout) => {
    if (timeout) {
      clearTimeout(timeout);
    }
  });
  // Clear the timeouts object
  Object.keys(saveTimeouts).forEach((key) => {
    delete saveTimeouts[Number(key)];
  });

  // Clear original scores tracking
  Object.keys(originalScores.value).forEach((key) => {
    delete originalScores.value[Number(key)];
  });
});
</script>

<style scoped>
/* Clean minimal styles using Quasar framework and app.scss classes */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* Use app.scss classes for consistency */
.bg-page-primary {
  min-height: 100vh;
}

/* Border classes for option styling */
.border-positive {
  border: 2px solid var(--q-positive) !important;
}

.border-negative {
  border: 2px solid var(--q-negative) !important;
}

.border-grey-4 {
  border: 1px solid var(--q-grey-4) !important;
}

/* Score input styling */
.score-input {
  width: 120px;
}

/* Simple responsive adjustments */
@media (max-width: 768px) {
  .container {
    padding: 15px;
  }

  .q-card {
    margin-bottom: 20px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 10px;
  }
}
</style>
