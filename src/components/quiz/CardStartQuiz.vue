<script setup lang="ts">
// import MyButton from '../common/MyButton.vue';

defineProps<{
  title: string;
  description: string;
  score: number;
  buttonLabel?: string;
}>();
</script>

<template>
  <q-card flat class="card_quiz">
    <div class="quiz-header">
      <div>
        <div class="quiz-title">{{ title }}</div>
        <div class="quiz-desc">{{ description }}</div>
      </div>
      <div class="quiz-score">{{ score }} คะแนน</div>
    </div>

    <div class="quiz-footer">
      <MyButton label="เริ่มทดสอบ"></MyButton>
    </div>
  </q-card>
</template>

<style scoped>
.card_quiz {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 40px;
  gap: 10px;
  position: relative;
  width: 100%;
  max-width: 1300px;
  height: 555px;
  background: #ffffff;
  box-shadow: 0px 4px 16px rgba(0, 0, 0, 0.35);
  border-radius: 12px;
  border: #666 solid 1px;
}

.quiz-header {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.quiz-title {
  font-size: 20px;
  font-weight: bold;
}

.quiz-desc {
  color: #666;
  margin-top: 4px;
}

.quiz-score {
  font-weight: bold;
  font-size: 18px;
}

.quiz-footer {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  margin-top: auto;
}

.quiz-button {
  padding: 6px 18px;
  border-radius: 8px;
  text-transform: none;
}
</style>
