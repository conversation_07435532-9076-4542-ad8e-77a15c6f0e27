<template>
  <q-dialog ref="dialogRef" persistent>
    <q-card class="container">
      <q-card-section>
        <div class="text-h6">{{ title }}</div>
        <q-separator class="q-my-sm" />
      </q-card-section>
      <q-form ref="formRef" @submit.prevent="submitForm">
        <q-card-section class="q-pt-none">
          <div class="q-gutter-y-md">
            <q-input
              v-model="formDataRef.name"
              label="ชื่อสมรรถนะ"
              :rules="[(val) => !!val || 'กรุณากรอกชื่อสมรรถนะ']"
              outlined
            />
            <q-select
              v-model="formDataRef.type"
              outlined
              label="ประเภทของสมรรถนะ"
              :options="[]"
            ></q-select>
            <q-select
              v-model="formDataRef.career"
              outlined
              label="ประเภทของสายงาน"
              :options="[]"
            ></q-select>
            <q-input
              v-model="formDataRef.description"
              label="คำอธิบายเพิ่มเติม"
              type="textarea"
              outlined
            />
          </div>
        </q-card-section>
        <q-card-actions align="center" class="row">
          <q-btn label="ยกเลิก" class="col" flat color="grey-7" @click="onDialogCancel" />
          <q-btn label="บันทึก" class="col" color="accent" @click="submitForm" />
        </q-card-actions>
      </q-form>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { QForm, useDialogPluginComponent } from 'quasar';
import type { Competency } from 'src/types/models';

const props = defineProps<{
  title: string;
  formData?: Competency;
}>();

// Properly destructure the dialog plugin component
const { dialogRef, onDialogOK, onDialogCancel } = useDialogPluginComponent();

const formRef = ref<QForm | null>(null);
const formDataRef = ref<Competency>({
  id: 0,
  name: '',
  description: '',
  type: '',
  career: '',
});

onMounted(() => {
  if (props.formData) {
    formDataRef.value = { ...props.formData };
  } else {
    formDataRef.value = {
      id: 0,
      name: '',
      description: '',
      type: '',
      career: '',
    };
  }
});

const submitForm = async () => {
  if (formRef.value) {
    try {
      const isValid = await formRef.value.validate();
      if (isValid) {
        onDialogOK(formDataRef.value);
      }
    } catch (error) {
      console.error('Form validation error:', error);
    }
  }
};
</script>

<style scoped></style>
