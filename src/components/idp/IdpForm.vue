<template>
  <q-dialog ref="dialogRef" persistent>
    <q-card class="container">
      <q-card-section>
        <div class="text-h6">{{ title }}</div>
        <q-separator class="q-my-sm" />
      </q-card-section>
      <q-form ref="formRef" @submit.prevent="submitForm">
        <q-card-section class="q-pt-none">
          <div class="q-gutter-y-md">
            <q-input
              v-model="formDataRef.name"
              label="ชื่อ"
              :rules="[(val) => !!val || 'กรุณากรอกชื่อสมรรถนะ']"
              outlined
            />
            <q-checkbox v-model="isCopyMode" label="สำเนาจากแผนอื่น"></q-checkbox>
            <q-select
              v-if="isCopyMode"
              label="แผน"
              outlined
              v-model="selectedPlan"
              :options="plans"
            ></q-select>
          </div>
        </q-card-section>
        <q-card-actions align="center" class="row">
          <q-btn label="ยกเลิก" class="col" flat color="grey-7" @click="onDialogCancel" />
          <q-btn label="บันทึก" class="col" color="positive" @click="submitForm" />
        </q-card-actions>
      </q-form>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { QForm, useDialogPluginComponent } from 'quasar';
import type { IDP } from 'src/types/models';

const props = defineProps<{
  title: string;
  formData?: IDP;
}>();

// Properly destructure the dialog plugin component
const { dialogRef, onDialogOK, onDialogCancel } = useDialogPluginComponent();

const selectedPlan = ref<number | null>(null);
const plans = ref<{ label: string; value: number }[]>([
  { label: 'แผน A', value: 1 },
  { label: 'แผน B', value: 2 },
  { label: 'แผน C', value: 3 },
]);
const isCopyMode = ref(false);
const formRef = ref<QForm | null>(null);
const formDataRef = ref<IDP>({
  id: 0,
  name: '',
});

onMounted(() => {
  if (props.formData) {
    formDataRef.value = { ...props.formData };
  } else {
    formDataRef.value = {
      id: 0,
      name: '',
    };
  }
});

const submitForm = async () => {
  if (formRef.value) {
    try {
      const isValid = await formRef.value.validate();
      if (isValid) {
        onDialogOK(formDataRef.value);
      }
    } catch (error) {
      console.error('Form validation error:', error);
    }
  }
};
</script>

<style scoped></style>
