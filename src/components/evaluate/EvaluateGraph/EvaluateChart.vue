<template>
  <div>
    <!-- Section Header -->
    <div v-if="isHeaderType" class="section-tab">ส่วนที่ {{ section[0] }} จาก {{ section[1] }}</div>

    <!-- Chart Card -->
    <q-card :class="['row justify-between items-center q-pa-md', cardClass]">
      <!-- Title & Controls -->
      <div class="q-mb-md row col-12 align-center">
        <!-- Title -->
        <div class="col-9 q-pt-md q-pl-lg">
          <strong class="title"> {{ chartTitle }} </strong>
        </div>

        <!-- Chart Selector -->
        <q-select
          v-if="enableControls"
          class="col-2"
          dense
          outlined
          v-model="selectedChart"
          :options="chartOptions"
          style="width: 150px"
          label="เลือกกราฟ"
        />

        <!-- Copy Button -->
        <div class="col-1 icon-wrapper" v-if="enableControls">
          <q-btn icon="content_copy" class="white-bg" @click="copyCardImage" />
        </div>
      </div>

      <!-- Chart Content -->
      <div class="col-12 chart-container">
        <!-- Bar Chart -->
        <BarChart
          v-if="selectedChart === 'กราฟแท่ง'"
          ref="barChartRef"
          :labels="computedChartData.labels"
          :data="computedChartData.datasets"
          class="bar-chart-responsive"
        />

        <!-- Pie Chart -->
        <div v-else-if="selectedChart === 'กราฟวงกลม'" class="pie-chart-scroll-wrapper">
          <div
            v-for="(dataset, index) in computedChartData.datasets"
            :key="index"
            class="q-mt-md pie-chart-wrapper"
          >
            <h4 v-if="hasTitle" class="question-label">
              {{ dataset.label }}
            </h4>
            <div v-else class="question-label" style="height: 40px" />

            <PieChart
              ref="pieChartRefs"
              :labels="computedChartData.labels"
              :data="dataset.values"
              :chartLabel="dataset.label"
            />
          </div>
        </div>

        <!-- Text Chart -->
        <div v-if="selectedChart === 'ข้อความ'" class="text-chart-container">
          <div
            v-for="(dataset, index) in computedChartData.datasets"
            :key="index"
            class="text-item"
          >
            <div class="text-label">{{ dataset.label }}</div>
            <div class="text-value">{{ dataset.values[0] }}</div>
          </div>
        </div>

        <!-- Image Upload -->
        <div v-if="selectedChart === 'image'">
          <FileUpload :data="computedChartData.datasets" :labels="computedChartData.labels" />
        </div>
      </div>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import BarChart from './BarChart.vue';
import PieChart from './PieChart.vue';
import FileUpload from './FileUpload.vue';

const props = defineProps<{
  chartData?: {
    section: string;
    title: string;
    labels: string[];
    datasets: {
      label: string;
      values: number[];
    }[];
    type: 'text' | 'choice' | 'header' | 'image';
  };
}>();
interface BarChartExposed {
  renderChart: () => void;
  canvas: HTMLCanvasElement | null;
}
// --- Refs and State ---
const Enable = ref(false);
const selectedChart = ref('ข้อความ');
const chartOptions = ['กราฟแท่ง', 'กราฟวงกลม'];
const section = ref<string[]>(props.chartData?.section.split(',') || []);
const barChartRef = ref<BarChartExposed | null>(null);
const pieChartRefs = ref<InstanceType<typeof PieChart>[] | null>(null);

const originalChartData = props.chartData ?? {
  title: 'No Data',
  labels: [],
  datasets: [],
};

// --- On Mounted: Setup Chart Type and Controls ---
onMounted(() => {
  switch (props.chartData?.type) {
    case 'text':
      selectedChart.value = 'ข้อความ';
      Enable.value = false;
      break;
    case 'choice':
      selectedChart.value = 'กราฟแท่ง';
      Enable.value = true;
      break;
    case 'header':
      selectedChart.value = 'header';
      Enable.value = false;
      break;
    case 'image':
      selectedChart.value = 'image';
      Enable.value = false;
      break;
  }
  console.log(props.chartData);
});

// --- Computed ---
const computedChartData = computed(() => {
  // กรณีอื่นๆ ใช้ transformChartData สำหรับกราฟวงกลม หรือส่ง originalChartData
  return selectedChart.value === 'กราฟวงกลม'
    ? transformChartData(originalChartData)
    : originalChartData;
});

const cardClass = computed(() =>
  props.chartData?.type === 'header' ? 'q-card-header' : 'q-card-normal',
);

const chartTitle = computed(() => {
  const rawTitle =
    props.chartData?.labels?.length === 1
      ? props.chartData.labels[0]
      : props.chartData?.title || '';
  if (props.chartData?.type === 'image') return props.chartData.title;
  // ลบเฉพาะ tag HTML ที่สมบูรณ์ โดยไม่แตะ text เช่น <b
  const parser = new DOMParser();
  const parsed = parser.parseFromString(rawTitle!, 'text/html');
  return parsed.body.textContent || '';
});

const enableControls = computed(() => Enable.value);
const isHeaderType = computed(() => props.chartData?.type === 'header');
const hasTitle = computed(
  () => props.chartData?.title !== '' && props.chartData?.labels.length !== 1,
);

// --- Methods ---
function transformChartData(data: typeof originalChartData): typeof originalChartData {
  const transposed = data.datasets[0]?.values.map((_, i) =>
    data.datasets.map((ds) => ds.values[i] ?? 0),
  );

  const newDatasets =
    transposed?.map((vals, idx) => ({
      label: data.labels[idx] ?? '',
      values: vals,
    })) ?? [];

  return {
    section: props.chartData?.section ?? '',
    title: data.title ?? '',
    labels: data.datasets.map((d) => d.label),
    datasets: newDatasets,
    type: props.chartData?.type ?? 'choice',
  };
}

async function copyCardImage() {
  try {
    let canvas: HTMLCanvasElement | null = null;
    const titlePadding = 20; // Padding for main title (mimicking q-pt-md, q-pl-lg)
    const labelPadding = 10; // Padding for dataset labels (mimicking q-mt-md)
    const titleFontSize = 16; // Font size for main title
    const labelFontSize = 14; // Font size for dataset labels (h4)
    const titleFont = `bold ${titleFontSize}px sans-serif`; // Mimic Quasar's default font for title
    const labelFont = `bold ${labelFontSize}px sans-serif`; // Mimic Quasar's default font for h4

    // Prepare main title and measure its dimensions
    const titleText = chartTitle.value || '';
    const tempCanvas = document.createElement('canvas');
    const tempCtx = tempCanvas.getContext('2d');
    if (!tempCtx) {
      console.error('Failed to get 2D context for temporary canvas');
      return;
    }
    tempCtx.font = titleFont;
    const titleMetrics = tempCtx.measureText(titleText);
    const titleHeight = titleFontSize + 10; // Vertical space for title

    // Handle Bar Chart (single canvas)
    if (selectedChart.value === 'กราฟแท่ง' && barChartRef.value) {
      const barChartElement = barChartRef.value;
      canvas = barChartElement.canvas;
      if (!canvas) {
        console.error('No canvas found for BarChart');
        return;
      }
      // Create a new canvas to include title
      const newCanvas = document.createElement('canvas');
      newCanvas.width = Math.max(canvas.width, titleMetrics.width + titlePadding * 2);
      newCanvas.height = canvas.height + titleHeight;
      const ctx = newCanvas.getContext('2d');
      if (!ctx) {
        console.error('Failed to get 2D context for new canvas');
        return;
      }
      // Draw main title
      ctx.font = titleFont;
      ctx.fillStyle = '#000'; // Adjust to match your theme
      ctx.fillText(titleText, titlePadding, titleFontSize + 5);
      // Draw chart
      ctx.drawImage(canvas, 0, titleHeight);
      canvas = newCanvas;
    }
    // Handle Pie Charts (multiple canvases with dataset labels)
    else if (
      selectedChart.value === 'กราฟวงกลม' &&
      pieChartRefs.value &&
      pieChartRefs.value.length > 0
    ) {
      const canvases = pieChartRefs.value
        .map((pieChart) => pieChart.$el.querySelector('canvas'))
        .filter((canvas): canvas is HTMLCanvasElement => !!canvas);

      if (canvases.length === 0) {
        console.error('No canvases found for PieCharts');
        return;
      }

      // Get dataset labels
      const datasetLabels = computedChartData.value.datasets.map((dataset) => dataset.label || '');

      // Measure dataset label dimensions
      tempCtx.font = labelFont;
      const labelMetrics = datasetLabels.map((label) => tempCtx.measureText(label));
      const labelHeight = labelFontSize + 10; // Vertical space for each label

      // Create a temporary canvas to combine title, labels, and pie charts
      const maxWidth = Math.max(
        ...canvases.map((c) => c.width),
        titleMetrics.width + titlePadding * 2,
        ...labelMetrics.map((m) => m.width + labelPadding * 2),
      );
      const totalChartHeight =
        canvases.reduce((sum, c) => sum + c.height, 0) +
        (canvases.length - 1) * 10 + // 10px gap between charts
        canvases.length * labelHeight; // Space for each dataset label
      canvas = document.createElement('canvas');
      canvas.width = maxWidth;
      canvas.height = totalChartHeight + titleHeight;

      const ctx = canvas.getContext('2d');
      if (!ctx) {
        console.error('Failed to get 2D context for temporary canvas');
        return;
      }

      // Draw main title
      ctx.font = titleFont;
      ctx.fillStyle = '#000'; // Adjust to match your theme
      ctx.fillText(titleText, titlePadding, titleFontSize + 5);

      // Draw each pie chart with its dataset label
      let currentY = titleHeight;
      canvases.forEach((pieCanvas, index) => {
        if (hasTitle.value && datasetLabels[index]) {
          // Draw dataset label
          ctx.font = labelFont;
          ctx.fillStyle = '#000'; // Adjust to match your theme
          ctx.fillText(datasetLabels[index], labelPadding, currentY + labelFontSize + 5);
          currentY += labelHeight;
        }
        // Draw pie chart
        ctx.drawImage(pieCanvas, 0, currentY);
        currentY += pieCanvas.height + 10; // Add gap between charts
      });
    } else {
      console.error('No valid chart selected or refs not found');
      return;
    }

    // Convert combined canvas to blob
    const blob = await new Promise<Blob | null>((resolve) => {
      canvas.toBlob((blob) => resolve(blob), 'image/png');
    });

    if (!blob) {
      console.error('Failed to create blob from canvas');
      return;
    }

    // Create clipboard item
    const clipboardItem = new ClipboardItem({ 'image/png': blob });

    // Write to clipboard
    await navigator.clipboard.write([clipboardItem]);
    console.log('Chart image copied to clipboard successfully');
  } catch (error) {
    console.error('Failed to copy chart image:', error);
  }
}
</script>

<style scoped lang="scss">
.q-card-header,
.q-card-normal {
  width: 1000px;
  max-width: 1000px;
  margin: 0 auto;
  background-color: white;
  border: 1px solid;
  border-radius: 12px;
}

.q-card-header {
  border-top-left-radius: 0;
}

.chart-container {
  padding: 0 5%;
  max-height: 600px;
  overflow-y: auto;
  box-sizing: border-box;
}

.pie-chart-scroll-wrapper {
  max-height: 600px;
  overflow-y: auto;
  padding-right: 10px;
}

.pie-chart-wrapper {
  width: 100%;
  max-width: 600px;
  margin: 10px auto;
  padding: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.pie-chart-wrapper canvas {
  width: 80% !important;
  max-height: 300px;
  object-fit: contain;
}

.bar-chart-responsive {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

.white-bg {
  width: 20px !important;
  background-color: white !important;
  color: black !important;
}

.white-bg .q-icon {
  color: black !important;
}

.icon-wrapper {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding-right: 4px;
}

.question-label {
  width: 100%;
  font-size: 20px;
  text-align: left;
  margin-bottom: 8px;
}

.text-chart-container {
  max-height: 300px;
  overflow-y: auto;
}

.text-item {
  background-color: lightgray;
  width: 800px;
  margin: 5px 0;
  padding: 10px;
  display: flex;
  justify-content: space-between;
  border-radius: 8px;
}

.text-label,
.text-value {
  padding: 5px 20px;
}

.text-value {
  text-align: right;
  padding-right: 50px;
}

.title {
  font-size: 20px;
  font-weight: bold;
}

.section-tab {
  background-color: #673ab7;
  color: white;
  font-weight: 500;
  padding: 6px 16px;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  font-size: 14px;
  width: fit-content;
}
</style>
