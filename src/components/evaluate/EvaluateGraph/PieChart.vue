<template>
  <div class="chart-wrapper">
    <canvas ref="canvas"></canvas>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch, defineExpose } from 'vue';
import { Chart, PieController, ArcElement, Tooltip, Legend } from 'chart.js';
import ChartDataLabels from 'chartjs-plugin-datalabels';

Chart.register(PieController, ArcElement, Tooltip, Legend, ChartDataLabels);

const props = defineProps<{
  labels: string[];
  data: number[];
}>();

const canvas = ref<HTMLCanvasElement | null>(null);
let chartInstance: Chart | null = null;

const pieColors = ['#3d3c91', '#5961b3', '#7883cc', '#a0aee6', '#c8cfff'];

function renderChart() {
  if (!canvas.value) return;
  if (chartInstance) chartInstance.destroy();

  const total = props.data.reduce((sum, value) => sum + value, 0);
  // ไม่แสดง legend และ tooltip ถ้ามี label เดียว
  const showLegendAndTooltip = props.labels.length > 1;
  // ใช้ labels เป็น [''] ถ้ามี label เดียว

  chartInstance = new Chart(canvas.value, {
    type: 'pie',
    data: {
      labels: props.labels,
      datasets: [
        {
          data: props.data,
          backgroundColor: pieColors.slice(0, props.data.length),
          borderColor: 'white',
          borderWidth: 2,
        },
      ],
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      layout: { padding: 0 },
      plugins: {
        ...(showLegendAndTooltip && {
          legend: {
            display: true,
            position: 'right',
            align: 'center',
            labels: {
              usePointStyle: true,
              boxWidth: 20,
              padding: 20,
              font: {
                size: 14,
                weight: 'bold',
              },
              generateLabels(chart) {
                const original = Chart.overrides.pie.plugins.legend.labels.generateLabels(chart);
                return original.map((label) => ({
                  ...label,
                  text: label.text.length > 15 ? label.text.slice(0, 15) + '…' : label.text,
                }));
              },
            },
          },
          tooltip: {
            enabled: true,
            callbacks: {
              label: (context) => {
                const fullLabel = props.labels[context.dataIndex];
                const value = context.formattedValue;
                return `${fullLabel}: ${value}`;
              },
            },
          },
        }),
        datalabels: {
          display: (context) => {
            const value = context?.dataset?.data?.[context.dataIndex];
            return typeof value === 'number' && value > 0;
          },
          color: 'white',
          anchor: 'center',
          align: 'center',
          formatter: (value: number) => {
            const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : '0.0';
            return `${percentage}%`;
          },
          font: {
            size: 14,
            weight: 'bold',
          },
        },
      },
    },
  });
}

onMounted(renderChart);

watch(() => [props.labels, props.data], renderChart, { deep: true });

onBeforeUnmount(() => {
  if (chartInstance) chartInstance.destroy();
});

defineExpose({ canvas });
</script>

<style scoped>
.chart-wrapper {
  width: 100%;
  max-width: 700px;
}
canvas {
  width: 100% !important;
  height: 300px !important;
}
</style>
