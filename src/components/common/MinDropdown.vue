<script setup lang="ts">
import { ref, watch } from 'vue';

const props = defineProps<{
  modelValue: number | null;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: number | null): void;
}>();

const selectedMinute = ref<number | null>(props.modelValue);

watch(
  () => props.modelValue,
  (newValue) => {
    selectedMinute.value = newValue;
  },
);

watch(selectedMinute, (val) => {
  emit('update:modelValue', val);
});

const minute = Array.from({ length: 60 }, (_, i) => ({
  label: `${i}`,
  value: i,
}));
</script>

<template>
  <q-select
    v-model="selectedMinute"
    :options="minute"
    emit-value
    map-options
    outlined
    dense
    dropdown-icon="arrow_drop_down"
    style="width: 100px"
    label="นาที"
  >
    <template #selected>
      <span class="custom-placeholder" v-if="selectedMinute === null">นาที</span>
      <span v-else>{{ selectedMinute }}</span>
    </template>
  </q-select>
</template>

<style scoped>
.custom-placeholder {
  color: #9e9e9e;
  font-size: 14px;
}
</style>
