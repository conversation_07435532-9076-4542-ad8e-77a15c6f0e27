<template>
  <q-page class="q-pa-md">
    <div v-if="loading" class="text-center q-pa-lg">
      <q-spinner size="lg" color="primary" />
      <div class="q-mt-md">Loading assessment...</div>
    </div>
    <div v-else-if="assessmentId">
      <!-- <BlockCreator :blocks="blocks" :assessmentId="assessmentId" type="evaluate" /> -->
      <!-- EvaluateEditorView.vue -->
      <BlockCreator :assessmentId="assessmentId" type="evaluate" />
    </div>
    <div v-else class="text-center q-pa-lg">
      <q-icon name="warning" size="lg" color="orange" />
      <div class="q-mt-md text-h6">Assessment ID not available</div>
      <div class="text-body2 text-grey-6">Please check the URL or try refreshing the page</div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import BlockCreator from 'src/components/common/blocks/BlockCreator.vue';
import { computed, onMounted, watch, ref } from 'vue';
import { useRoute } from 'vue-router';
import { useBlockCreatorStore } from 'src/stores/block_creator';

const blockCreatorStore = useBlockCreatorStore();
const route = useRoute();

// ✅ ENHANCED ASSESSMENT ID with multiple fallbacks
const assessmentId = computed(() => {
  // Primary: Get from store's current assessment
  const storeAssessmentId = blockCreatorStore.currentAssessment?.id;
  if (storeAssessmentId) {
    return storeAssessmentId;
  }

  // Fallback 1: Get from route parameters
  const routeId = Number(route.params.id);
  if (routeId && routeId > 0) {
    console.log('🔄 [EVALUATE-EDITOR] Using assessment ID from route:', routeId);
    return routeId;
  }

  // Fallback 2: Get from store's getAssessmentId method
  const storeMethodId = blockCreatorStore.getAssessmentId();
  if (storeMethodId) {
    console.log('🔄 [EVALUATE-EDITOR] Using assessment ID from store method:', storeMethodId);
    return storeMethodId;
  }

  console.warn('⚠️ [EVALUATE-EDITOR] No assessment ID available from any source');
  return null;
});

// const blocks = computed(() => {
//   const assessmentBlocks = blockCreatorStore.currentAssessment?.itemBlocks;

//   if (assessmentBlocks && assessmentBlocks.length > 0) {
//     return assessmentBlocks;
//   } else {
//     return [];
//   }
// });
// ใน script setup
const loading = ref(true);

async function fetchAssessment() {
  loading.value = true;
  try {
    const id = route.params.id;
    if (id) {
      const numericId = Number(id);
      console.log('🔄 [EVALUATE-EDITOR] Fetching assessment:', numericId);

      await blockCreatorStore.fetchAssessmentById(numericId);

      // ✅ VERIFY ASSESSMENT WAS LOADED
      const loadedAssessment = blockCreatorStore.currentAssessment;
      if (loadedAssessment?.id === numericId) {
        console.log('✅ [EVALUATE-EDITOR] Assessment loaded successfully:', {
          assessmentId: loadedAssessment.id,
          itemBlocksCount: loadedAssessment.itemBlocks?.length || 0,
        });
      } else {
        console.error('❌ [EVALUATE-EDITOR] Assessment not loaded properly:', {
          expectedId: numericId,
          loadedId: loadedAssessment?.id,
          hasAssessment: !!loadedAssessment,
        });
      }
    }
  } catch (error) {
    console.error('❌ [EVALUATE-EDITOR] Failed to fetch assessment:', error);
  } finally {
    loading.value = false;
  }
}

onMounted(fetchAssessment);

watch(
  () => route.params.id,
  async (newId, oldId) => {
    if (newId && newId !== oldId) {
      await fetchAssessment();
    }
  },
);
</script>
<style scoped></style>
