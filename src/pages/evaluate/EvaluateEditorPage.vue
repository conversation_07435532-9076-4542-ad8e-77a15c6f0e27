<template>
  <q-page class="bg-page-primary">
    <div class="sticky-menu">
      <AsmMenuTab :menu="defaultAsmTabsMenu" v-model="selectedTab" />
    </div>
    <q-tab-panels v-model="selectedTab" animated>
      <q-tab-panel
        v-for="tab in defaultAsmTabsMenu"
        :name="tab.name ?? ''"
        :key="tab.name ?? ''"
        class="bg-page-primary"
      >
        <component :is="componentMap[tab.name as string]" v-if="selectedTab === tab.name" />
      </q-tab-panel>
    </q-tab-panels>
  </q-page>
</template>

<script setup lang="ts">
import AsmMenuTab from 'src/components/common/AsmMenuTab.vue';
import { defaultAsmTabsMenu } from 'src/data/menu';
// import { AssessmentService } from 'src/services/asm/assessmentService';
// import { useAssessmentStore } from 'src/stores/asm';
import { useBlockCreatorStore } from 'src/stores/block_creator';
import { onMounted, ref, defineAsyncComponent } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();
const id = route.params.id;
const hash = route.hash;
const blockCreatorStore = useBlockCreatorStore();
// const asmStore = useAssessmentStore();
const selectedTab = ref('questions');

onMounted(() => {
  if (hash) {
    selectedTab.value = hash.replace('#', '');
  } else {
    selectedTab.value = 'questions';
  }
});

const componentMap: Record<string, ReturnType<typeof defineAsyncComponent>> = {
  questions: defineAsyncComponent(() => import('./tabs/EvaluateEditorView.vue')),
  replies: defineAsyncComponent(() => import('./tabs/EvaluateReplyView.vue')),
  settings: defineAsyncComponent(() => import('./tabs/EvaluateSettingView.vue')),
};

onMounted(async () => {
  try {
    // Check if assessment is already in store (from creation flow)
    if (blockCreatorStore.currentAssessment?.id === Number(id)) {
      console.log('Assessment already in store from creation:', {
        assessmentId: blockCreatorStore.currentAssessment.id,
        itemBlocks: blockCreatorStore.currentAssessment.itemBlocks?.map((block) => ({
          id: block.id,
          type: block.type,
          assessmentId: block.assessmentId,
        })),
      });
      return;
    }

    // Otherwise, fetch from backend
    await blockCreatorStore.fetchAssessmentById(Number(id));
    blockCreatorStore.initializeBlocks(blockCreatorStore.currentAssessment?.itemBlocks || []);
  } catch {
    // Failed to load assessment
  }
});
</script>

<style scoped>
.sticky-menu {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: var(--q-page-bg, #f5f5ff);
  padding-bottom: 10px;
}
</style>
