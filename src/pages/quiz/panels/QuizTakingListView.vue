<template>
  <div class="quiz-taking-list-container">
    <!-- Search Bar -->
    <div class="search-container q-mb-md">
      <SearchBar @search="handleSearch" />
    </div>

    <!-- Error Banner -->
    <q-banner
      v-if="dashboardStore.error"
      inline-actions
      class="text-white bg-red q-mb-md rounded-borders"
    >
      <template v-slot:avatar>
        <q-icon name="error_outline" color="white" />
      </template>
      {{ dashboardStore.error }}
      <template v-slot:action>
        <q-btn flat color="white" label="Retry" @click="retryFetch" :loading="isLoading" />
        <q-btn flat color="white" label="Clear" @click="dashboardStore.clearError" />
      </template>
    </q-banner>

    <!-- QTable -->
    <q-table
      :rows="tableRows"
      :columns="columns"
      row-key="id"
      :loading="isLoading"
      v-model:pagination="paginationState"
      @request="handleTableRequest"
      :rows-per-page-options="[5, 10, 15, 25, 50]"
      binary-state-sort
      flat
      bordered
      separator="cell"
      class="q-mt-md animate-table"
      table-header-class="bg-primary text-black text-h6"
      :loading-delay="100"
      :table-style="{ transition: 'all 0.3s ease-out' }"
      :loading-spinner-size="40"
    >
      <template v-slot:body-cell-userName="{ row }">
        <q-td class="text-left">{{ row.userName }}</q-td>
      </template>

      <template v-slot:body-cell-date="{ row }">
        <q-td class="text-center">{{ row.date }}</q-td>
      </template>

      <template v-slot:body-cell-score="{ row }">
        <q-td
          :class="[
            'text-center',
            row.score < (dashboardStore.assessmentMeta?.highestScore ?? 100) / 2
              ? 'text-negative'
              : 'text-positive',
          ]"
        >
          {{ row.score }}
        </q-td>
      </template>

      <template v-slot:body-cell-actions="{ row }">
        <q-td class="text-center">
          <q-btn
            dense
            unelevated
            class="edit-graph-icon"
            icon="article"
            @click="viewUserAnswer(row.id)"
            aria-label="View Answers"
          >
            <q-tooltip>ดูคำตอบ</q-tooltip>
          </q-btn>
        </q-td>
      </template>

      <template v-slot:no-data="{ icon, message, filter }">
        <div class="full-width row flex-center text-accent q-gutter-sm q-pa-md">
          <q-icon size="2em" :name="filter ? 'filter_b_and_w' : icon" />
          <span>
            {{
              props.assessmentId === null && !isLoading
                ? 'Please select an assessment first.'
                : message
            }}
          </span>
        </div>
      </template>
    </q-table>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from 'vue';
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import type { QTableProps } from 'quasar';

import type { DataParams } from 'src/types/data';
import { useQuizDashboardStore } from 'src/stores/quizdashboardStore';
import SearchBar from 'src/components/SearchBar.vue';

const props = defineProps({
  assessmentId: {
    type: Number as PropType<number | null>,
    required: true,
  },
});

const dashboardStore = useQuizDashboardStore();
const router = useRouter();
const paginationState = ref<Required<QTableProps['pagination']>>({
  sortBy: 'date',
  descending: true,
  page: 1,
  rowsPerPage: 10,
  rowsNumber: 0,
});

// --- Computed Properties ---
const isLoading = computed(() => {
  return dashboardStore.isLoadingParticipants;
});

const tableRows = computed(() => {
  return dashboardStore.participants?.data || [];
});

// --- Columns Definition ---
const columns: QTableProps['columns'] = [
  {
    name: 'id',
    label: 'รหัส',
    align: 'center',
    field: 'id',
    sortable: true,
    required: true,
  },
  {
    name: 'date',
    label: 'เวลาที่เริ่มทำ',
    align: 'center',
    field: 'date',
    sortable: true,
    required: true,
  },
  {
    name: 'userName',
    label: 'ชื่อผู้ทำแบบทดสอบ',
    align: 'left',
    field: 'userName',
    sortable: true,
    required: true,
  },
  {
    name: 'score',
    label: 'คะแนน',
    align: 'center',
    field: 'score',
    sortable: true,
    required: true,
  },
  {
    name: 'actions',
    label: 'กระดาษคำตอบ',
    align: 'center',
    field: () => '',
  },
];

// --- Methods ---
async function handleTableRequest(requestProps: {
  pagination: QTableProps['pagination'];
  filter?: string;
}) {
  const newPagination = requestProps.pagination;
  console.log('[QuizTakerList] handleTableRequest triggered. Assessment ID:', props.assessmentId);

  if (props.assessmentId === null) {
    console.warn('[QuizTakerList] handleTableRequest: assessmentId is null, aborting fetch.');
    return;
  }

  // Update local pagination state
  if (paginationState.value && newPagination) {
    paginationState.value.page = newPagination.page ?? 1;
    paginationState.value.rowsPerPage = newPagination.rowsPerPage ?? 10;
    paginationState.value.sortBy = newPagination.sortBy ?? null;
    paginationState.value.descending = newPagination.descending ?? false;
  }
  const params: DataParams = {
    page: paginationState.value?.page ?? 1,
    limit: paginationState.value?.rowsPerPage ?? 10,
    sortBy: paginationState.value?.sortBy ?? 'date',
    order: paginationState.value?.descending ? 'DESC' : 'ASC',
    search: null,
  };

  console.log('[QuizTakerList] Calling fetchParticipants with params:', params);
  await dashboardStore.fetchParticipants(props.assessmentId, params);
}

async function retryFetch() {
  console.log('[QuizTakerList] retryFetch called.');
  dashboardStore.clearError();
  if (props.assessmentId !== null && paginationState.value) {
    await handleTableRequest({ pagination: paginationState.value });
  }
}

async function viewUserAnswer(participantId: number) {
  console.log('[QuizTakerList] viewUserAnswer called for participant:', participantId);
  // Navigate to participant details page
  await router.push({
    name: 'ParticipantDetails',
    params: { participantId: participantId.toString() },
  });
}

// Search handler
const handleSearch = async (searchValue: string) => {
  if (props.assessmentId === null || !paginationState.value) return;

  const params: DataParams = {
    page: 1, // Reset to first page on search
    limit: paginationState.value.rowsPerPage,
    sortBy: paginationState.value.sortBy ?? 'date',
    order: paginationState.value.descending ? 'DESC' : 'ASC',
    search: searchValue,
  };

  await dashboardStore.fetchParticipants(props.assessmentId, params);
};

// --- Watchers ---
watch(
  () => props.assessmentId,
  async (newAssessmentId, oldAssessmentId) => {
    console.log(
      `[QuizTakerList] assessmentId watcher triggered. New: ${newAssessmentId}, Old: ${oldAssessmentId}`,
    );
    if (newAssessmentId !== oldAssessmentId) {
      if (paginationState.value) {
        paginationState.value.page = 1;
      }
      if (newAssessmentId !== null && paginationState.value) {
        await handleTableRequest({ pagination: paginationState.value });
      }
    }
  },
  { immediate: true },
);

watch(
  () => dashboardStore.participants,
  (newParticipants) => {
    console.log('[QuizTakerList] participants watcher triggered');
    if (paginationState.value) {
      if (newParticipants) {
        paginationState.value.rowsNumber = newParticipants.total;
        paginationState.value.page = newParticipants.curPage;
      } else {
        paginationState.value.rowsNumber = 0;
      }
    }
  },
  { deep: true },
);

// --- Lifecycle Hooks ---
onMounted(() => {
  console.log(`[QuizTakerList] Component mounted. Initial assessmentId: ${props.assessmentId}`);
});
</script>

<style scoped>
.quiz-taking-list-container {
  animation: fadeSlideUp 0.4s ease-out; /* เร็วขึ้นจาก 0.8s */
  overflow-x: hidden;
  position: relative;
  width: 100%;
}

.edit-graph-icon {
  background-color: #673ab7;
  color: white;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.edit-graph-icon:hover {
  background-color: #5e35b1;
  transform: scale(1.1);
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
}

:deep(.q-table) {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

:deep(.q-table:hover) {
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
}

:deep(.q-table thead th) {
  font-size: 1.1rem;
  padding-top: 16px;
  padding-bottom: 16px;
  background: #ffca28; /* Darker amber yellow */
  color: #000000 !important;
  font-weight: 600;
  border-bottom: 2px solid #ffa000;
  transition: all 0.3s ease;
  transform-origin: center;
}

:deep(.q-table thead th:hover) {
  background: #ffa000; /* Even darker on hover */
  transform: scale(1.02);
}

:deep(.q-table tbody td) {
  font-size: 1.1rem;
  padding-top: 12px;
  padding-bottom: 12px;
  vertical-align: middle;
  line-height: 1.6;
  transition: background-color 0.3s ease;
}

:deep(.q-table tbody tr:hover td) {
  background-color: #f8f8f8;
}

:deep(.q-table tbody td[data-label='รหัส']),
:deep(.q-table tbody td[data-label='คะแนน']) {
  text-align: center;
}

.search-container {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 1rem;
  opacity: 0;
  animation: fadeSlideIn 0.3s ease-out 0.1s forwards; /* เร็วขึ้นจาก 0.6s */
}

.q-table {
  opacity: 0;
  animation: fadeSlideIn 0.3s ease-out 0.2s forwards; /* เร็วขึ้นจาก 0.6s */
}

/* ปรับ animation ให้เร็วขึ้น */
:deep(.q-table tbody tr) {
  opacity: 0;
  animation: fadeIn 0.2s ease-out forwards;
}

:deep(.q-table tbody tr:nth-child(1)) {
  animation-delay: 0.2s;
}
:deep(.q-table tbody tr:nth-child(2)) {
  animation-delay: 0.25s;
}
:deep(.q-table tbody tr:nth-child(3)) {
  animation-delay: 0.3s;
}
:deep(.q-table tbody tr:nth-child(4)) {
  animation-delay: 0.35s;
}
:deep(.q-table tbody tr:nth-child(5)) {
  animation-delay: 0.4s;
}
:deep(.q-table tbody tr:nth-child(n + 6)) {
  animation-delay: 0.45s;
}

/* ปรับ table layout เพื่อป้องกัน scrollbar */
:deep(.q-table__container) {
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

:deep(.q-table) {
  width: 100%;
}

:deep(.q-table__middle) {
  overflow-x: hidden !important;
}

:deep(.q-table thead tr) {
  width: 100%;
  display: table;
  table-layout: fixed;
}

:deep(.q-table tbody tr) {
  width: 100%;
  display: table;
  table-layout: fixed;
}

/* ปรับความกว้างคอลัมน์ให้เหมาะสม */
:deep(.q-table) th:nth-child(1),
:deep(.q-table) td:nth-child(1) {
  width: 10% !important;
}

:deep(.q-table) th:nth-child(2),
:deep(.q-table) td:nth-child(2) {
  width: 20% !important;
}

:deep(.q-table) th:nth-child(3),
:deep(.q-table) td:nth-child(3) {
  width: 40% !important;
}

:deep(.q-table) th:nth-child(4),
:deep(.q-table) td:nth-child(4) {
  width: 15% !important;
}

:deep(.q-table) th:nth-child(5),
:deep(.q-table) td:nth-child(5) {
  width: 15% !important;
}

/* Responsive adjustments */
@media screen and (max-width: 600px) {
  :deep(.q-table) th:nth-child(1),
  :deep(.q-table) td:nth-child(1) {
    width: 15% !important;
  }

  :deep(.q-table) th:nth-child(2),
  :deep(.q-table) td:nth-child(2) {
    width: 25% !important;
  }

  :deep(.q-table) th:nth-child(3),
  :deep(.q-table) td:nth-child(3) {
    width: 30% !important;
  }

  :deep(.q-table) th:nth-child(4),
  :deep(.q-table) td:nth-child(4) {
    width: 15% !important;
  }

  :deep(.q-table) th:nth-child(5),
  :deep(.q-table) td:nth-child(5) {
    width: 15% !important;
  }

  .edit-graph-icon {
    padding: 4px;
    min-height: 24px;
    min-width: 24px;
  }
}

/* Enhanced Animations */
@keyframes fadeSlideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media screen and (max-width: 600px) {
  :deep(.q-table) {
    font-size: 0.9rem;
  }

  :deep(.q-table thead th) {
    font-size: 0.95rem;
    padding: 8px 4px;
  }

  :deep(.q-table tbody td) {
    font-size: 0.9rem;
    padding: 8px 4px;
  }

  .edit-graph-icon {
    padding: 4px;
  }

  :deep(.q-table__container) {
    overflow-x: auto;
  }

  .search-container {
    margin-bottom: 0.5rem;
  }
}

/* Enhanced Table Animations */
:deep(.q-table tbody tr) {
  opacity: 0;
  animation: fadeIn 0.2s ease-out forwards;
}

:deep(.q-table tbody tr:nth-child(1)) {
  animation-delay: 0.2s;
}
:deep(.q-table tbody tr:nth-child(2)) {
  animation-delay: 0.25s;
}
:deep(.q-table tbody tr:nth-child(3)) {
  animation-delay: 0.3s;
}
:deep(.q-table tbody tr:nth-child(4)) {
  animation-delay: 0.35s;
}
:deep(.q-table tbody tr:nth-child(5)) {
  animation-delay: 0.4s;
}
:deep(.q-table tbody tr:nth-child(n + 6)) {
  animation-delay: 0.45s;
}

/* Enhance hover effects */
:deep(.q-table tbody td:first-child),
:deep(.q-table thead th:first-child) {
  position: relative;
  border-left: none;
}

:deep(.q-table tbody td:first-child)::before,
:deep(.q-table thead th:first-child)::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 5px;
  background-color: transparent;
  transition: all 0.3s ease;
}

:deep(.q-table tbody tr:hover td:first-child)::before {
  background-color: #ffca28;
}

:deep(.q-table thead th:first-child:hover)::before {
  background-color: #ffa000;
}

:deep(.q-table tbody tr:hover td) {
  background-color: rgba(255, 202, 40, 0.1) !important;
}

/* ปรับพื้นหลังตาราง */
:deep(.q-table__container) {
  background: white;
}

:deep(.q-table) {
  background: white;
}

:deep(.q-table__middle) {
  background: white;
}

:deep(.q-table tbody) {
  background: white;
}

/* Better mobile experience for error banner */
@media screen and (max-width: 600px) {
  .bg-red {
    margin: 0.5rem;
    font-size: 0.9rem;
  }

  .bg-red .q-btn {
    padding: 4px 8px;
    font-size: 0.8rem;
  }

  :deep(.q-table__no-data) {
    padding: 24px 0;
  }

  :deep(.q-table__no-data span) {
    font-size: 0.95rem;
  }
}

/* Loading state animation enhancement */
:deep(.q-table--loading thead tr:last-child th) {
  position: relative;
  overflow: hidden;
}

:deep(.q-table--loading thead tr:last-child th)::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 200%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: loading-shine 1.5s infinite;
}

@keyframes loading-shine {
  to {
    transform: translateX(100%);
  }
}

/* Other styles remain unchanged */
.bg-red {
  animation: slideIn 0.5s ease-out;
}

.bg-red .q-banner__content {
  font-size: 1rem;
}

.bg-red .q-btn {
  font-weight: 500;
  transition: all 0.3s ease;
}

.bg-red .q-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
}

:deep(.q-table__no-data) {
  padding: 48px 0;
  animation: fadeIn 0.5s ease-out;
}

:deep(.q-table__no-data span) {
  font-size: 1.1rem;
  color: #555;
}

:deep(.q-table__no-data .q-icon) {
  color: #777;
  animation: bounce 2s infinite;
}

@keyframes slideIn {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes bounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Add some nice transition for score colors */
:deep(.text-negative),
:deep(.text-positive) {
  transition: color 0.3s ease;
  font-weight: 600;
}

:deep(.text-negative) {
  color: #ff5252 !important;
}

:deep(.text-positive) {
  color: #4caf50 !important;
}
</style>
