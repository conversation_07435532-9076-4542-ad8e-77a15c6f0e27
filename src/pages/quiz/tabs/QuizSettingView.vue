<template>
  <div class="fit q-mx-auto row justify-center q-mt-xl">
    <q-card class="q-pa-xl container">
      <q-card-section class="text-h4 text-weight-bold"
        >ตั้งค่า
        <q-separator class="q-mt-lg" />
      </q-card-section>

      <q-card-section class="q-gutter-y-md">
        <div class="fit row items-center justify-between">
          <div>
            <div class="text-subtitle1">กำหนดขอบเขตเวลา</div>
            <div class="text-caption text-grey">
              กำหนดวันที่และเวลาเพื่อเปิด-ปิดแบบทดสอบแบบอัตโนมัติ
            </div>
          </div>

          <div class="row q-gutter-x-lg">
            <SelectDate v-model:model-value="selectedDate" label="เลือกวันเริ่มต้น" />
            <SelectDate
              v-model:model-value="selectedDateEnd"
              label="เลือกวันสิ้นสุด"
              :disable="selectedDate === '' || selectedDate === null"
              :rules="[
                (val: any) =>
                  !val ||
                  val >= selectedDate ||
                  'วันที่สิ้นสุดต้องมากกว่าหรือเท่ากับวันที่เริ่มต้น',
              ]"
            />
          </div>
        </div>

        <q-separator />
        <div class="row items-center justify-between">
          <div>
            <div class="text-subtitle1">ตั้งเวลาทำแบบทดสอบ</div>
            <div class="text-caption text-grey">กำหนดเวลาในการทำแบบทดสอบของผู้ทำแบบสอบถาม</div>
          </div>
          <div class="row q-gutter-x-md">
            <HourDropdown v-model:model-value="hour" />
            <MinDropdown v-model:model-value="minute" />
          </div>
        </div>

        <q-separator />
        <div class="row items-center justify-between">
          <div>
            <div class="text-subtitle1">จำนวนครั้งที่สามารถทำแบบทดสอบ</div>
            <div class="text-caption text-grey">
              กำหนดจำนวนครั้งที่สามารถทำแบบทดสอบของผู้ทำแบบสอบถาม
            </div>
          </div>
          <TextField
            v-model:model-value="attemptLimit"
            placeholder="กรุณากรอกข้อมูล..."
            type="number"
          />
        </div>

        <q-separator />
        <div class="row items-center justify-between">
          <div>
            <div class="text-subtitle1">ร้อยละขั้นต่ำเพื่อผ่านแบบทดสอบ</div>
            <div class="text-caption text-grey">กำหนดเกณฑ์คะแนนผ่านของแบบสอบถาม</div>
          </div>
          <TextField
            v-model:model-value="passRatio"
            placeholder="กรุณากรอกข้อมูล..."
            type="number"
          />
        </div>

        <q-separator />
        <div class="row items-center justify-between">
          <div>
            <div class="text-subtitle1">เป็นต้นแบบ</div>
            <div class="text-caption text-grey">กำหนดให้ฟอร์มนี้เป็นต้นแบบสำหรับสำเนาเท่านั้น</div>
          </div>
          <Toggle v-model:model-value="isPrototype" />
        </div>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref, watch } from 'vue';
import SelectDate from 'src/components/common/SelectDate.vue';
import TextField from 'src/components/common/TextField.vue';
import HourDropdown from 'src/components/common/HourDropdown.vue';
import MinDropdown from 'src/components/common/MinDropdown.vue';
import Toggle from 'src/components/common/ToggleBtn.vue';
import { useRoute } from 'vue-router';
import { debounce } from 'quasar';
import { AssessmentService } from 'src/services/asm/assessmentService';
import { useAssessmentStore } from 'src/stores/asm';
import { useGlobalStore } from 'src/stores/global';

const route = useRoute();
const paramId = route.params.id as string;
const assessment = new AssessmentService('quiz');
const assessmentStore = useAssessmentStore();
const globalStore = useGlobalStore();

const selectedDate = ref<string>('');
const selectedDateEnd = ref<string>('');
const hour = ref<number | null>(null);
const minute = ref<number | null>(null);
const attemptLimit = ref<number>(0);
const passRatio = ref<number>(0);
const isPrototype = ref<boolean>(false);

const convertSecondsToHourMinute = (seconds: number) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  return { hours, minutes };
};

const convertToSeconds = (hours: number, minutes: number) => {
  return hours * 3600 + minutes * 60;
};

const convertToDecimal = (percentage: number): number => {
  return percentage / 100;
};

const convertToPercentage = (decimal: number): number => {
  return decimal * 100;
};

const saveAssessment = debounce(async () => {
  if (assessmentStore.currentAssessment) {
    try {
      const assessmentToSave = { ...assessmentStore.currentAssessment };
      globalStore.startSaveOperation('Saving...');
      // แปลงวันที่เป็น Date หรือ null
      if (selectedDate.value) {
        assessmentToSave.startAt = selectedDate.value;
      } else {
        delete assessmentToSave.startAt;
      }

      if (selectedDateEnd.value) {
        assessmentToSave.endAt = selectedDateEnd.value;
      } else {
        delete assessmentToSave.endAt;
      }

      // จัดการ timeout
      if (hour.value !== null && minute.value !== null && hour.value >= 0 && minute.value >= 0) {
        assessmentToSave.timeout = convertToSeconds(hour.value, minute.value);
      } else {
        assessmentToSave.timeout = 0;
      }

      // จัดการ submitLimit
      assessmentToSave.submitLimit = Number(attemptLimit.value) || -1;

      // จัดการ passRatio
      if (passRatio.value !== undefined && passRatio.value !== null) {
        const validatedPercentage = Math.min(Math.max(Number(passRatio.value), 0), 100);
        assessmentToSave.passRatio = convertToDecimal(validatedPercentage);
      } else {
        assessmentToSave.passRatio = 0.5;
      }

      assessmentToSave.isPrototype = Boolean(isPrototype.value);

      await assessment.updateOne(Number(paramId), assessmentToSave);
      globalStore.completeSaveOperation(true, 'Saved successfully');
    } catch (error) {
      console.error('Error saving assessment:', error);
      globalStore.completeSaveOperation(false, 'Saved successfully');
    }
  } else {
    console.error('No assessment data to save');
    globalStore.completeSaveOperation(false, 'Saved successfully');
  }
}, 500);

// onMounted(async () => {
//   try {
//     const response = await assessment.fetchOne(Number(paramId));
//     assessmentStore.setCurrentAssessment(response);
//     if (assessmentStore.currentAssessment) {
//       selectedDate.value = assessmentStore.currentAssessment.startAt || '';
//       selectedDateEnd.value = assessmentStore.currentAssessment.endAt || '';

//       if (response.timeout) {
//         const { hours, minutes } = convertSecondsToHourMinute(response.timeout);
//         hour.value = hours ?? null;
//         minute.value = minutes ?? null;
//       } else {
//         hour.value = null;
//         minute.value = null;
//       }

//       attemptLimit.value = assessmentStore.currentAssessment.submitLimit ?? -1;
//       passRatio.value = assessmentStore.currentAssessment.passRatio
//         ? convertToPercentage(assessmentStore.currentAssessment.passRatio)
//         : 0;
//       isPrototype.value = assessmentStore.currentAssessment.isPrototype ?? false;
//     } else {
//       console.error('No assessment data found');
//     }
//   } catch (error) {
//     console.error('Error fetching assessment:', error);
//   }
// });

onMounted(async () => {
  try {
    const { assessment: assessmentData } = await assessment.fetchOne(Number(paramId));
    assessmentStore.setCurrentAssessment(assessmentData);

    if (assessmentStore.currentAssessment) {
      selectedDate.value = assessmentStore.currentAssessment.startAt || '';
      selectedDateEnd.value = assessmentStore.currentAssessment.endAt || '';

      if (assessmentData.timeout) {
        const { hours, minutes } = convertSecondsToHourMinute(assessmentData.timeout);
        hour.value = hours ?? null;
        minute.value = minutes ?? null;
      } else {
        hour.value = null;
        minute.value = null;
      }

      attemptLimit.value = assessmentStore.currentAssessment.submitLimit ?? -1;
      passRatio.value = assessmentStore.currentAssessment.passRatio
        ? convertToPercentage(assessmentStore.currentAssessment.passRatio)
        : 0;
      isPrototype.value = assessmentStore.currentAssessment.isPrototype ?? false;
    } else {
      console.error('No assessment data found');
    }
  } catch (error) {
    console.error('Error fetching assessment:', error);
  }
});

onUnmounted(() => {
  if (assessmentStore.currentAssessment) {
    assessmentStore.clearCurrentAssessment();
  }
});

watch(selectedDate, (newValue) => {
  if (assessmentStore.currentAssessment) {
    assessmentStore.currentAssessment.startAt = newValue ? newValue : '';
    void saveAssessment();
  }
});

watch(selectedDateEnd, (newValue) => {
  if (assessmentStore.currentAssessment) {
    assessmentStore.currentAssessment.endAt = newValue ? newValue : '';
    void saveAssessment();
  }
});

watch([hour, minute], ([newHour, newMin]) => {
  if (assessmentStore.currentAssessment && newHour !== null && newMin !== null) {
    assessmentStore.currentAssessment.timeout = convertToSeconds(newHour, newMin);
    void saveAssessment();
  }
});

watch(attemptLimit, (newValue) => {
  if (assessmentStore.currentAssessment) {
    assessmentStore.currentAssessment.submitLimit = Number(newValue) || -1;
    void saveAssessment();
  }
});

watch(passRatio, (newValue) => {
  if (assessmentStore.currentAssessment && newValue !== undefined && newValue !== null) {
    const validatedPercentage = Math.min(Math.max(Number(newValue), 0), 100);
    const decimalValue = convertToDecimal(validatedPercentage);
    assessmentStore.currentAssessment.passRatio = decimalValue;
    void saveAssessment();
  } else {
    console.warn('passRatio is undefined or null, skipping save');
  }
});

watch(isPrototype, (newValue) => {
  if (assessmentStore.currentAssessment) {
    assessmentStore.currentAssessment.isPrototype = Boolean(newValue);
    void saveAssessment();
  }
});
</script>

<style scoped>
.custom-card {
  width: 1100px;
  height: 600px;
  border-radius: 12px;
  flex: none;
  order: 0;
  flex-grow: 0;
}
</style>
