<template>
  <q-page padding>
    <!-- หัวข้อ -->
    <div class="text-h6 q-mb-md">แบบทดสอบทั้งหมด</div>

    <!-- แถบบน: Search + เพิ่ม -->
    <div class="row items-center q-gutter-sm justify-end q-mb-md">
      <SearchBar @search="onSearchUpdate" />
    </div>

    <q-table
      :rows="rows"
      :columns="quizUserColumns"
      row-key="id"
      flat
      bordered
      wrap-cells
      separator="cell"
      :loading="Loading.isActive"
      v-model:pagination="pagination"
      @request="handleRequest"
      binary-state-sort
    >
      <template v-slot:body-cell-link="{ row }">
        <q-td class="text-center">
          <q-btn flat dense icon="link" :disable="!row.assessmentLink" aria-label="Open link" />
        </q-td>
      </template>

      <!-- Actions Column (เหมือนเดิม) -->
      <template v-slot:body-cell-actions="{ row }">
        <q-td class="text-center">
          <div class="q-gutter-x-sm flex justify-center">
            <q-btn dense unelevated class="view-icon" icon="article" @click="onClickPreview(row)" />
          </div>
        </q-td>
      </template>
    </q-table>

    <ConfirmDialog
      v-model="confirmDialogVisible"
      :title="titleDialog"
      @confirm="onConfirmDelete"
      @cancel="onCancelDelete"
    />
  </q-page>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { useQuasar, Loading } from 'quasar';
import { quizUserColumns } from 'src/data/table_columns';
import { defaultPaginationValue } from 'src/configs/app.config';
import type { Assessment } from 'src/types/models';
import SearchBar from 'src/components/SearchBar.vue';
import ConfirmDialog from 'src/components/ConfirmDialog.vue';
import type { QTableProps } from 'quasar';
import { AssessmentService } from 'src/services/asm/assessmentService';
import { useGlobalStore } from 'src/stores/global';

const globalStore = useGlobalStore();
const router = useRouter();
const pagination = ref<QTableProps['pagination']>({ ...defaultPaginationValue });
const search = ref<string>('');
const rows = ref<Assessment[]>([]);
const confirmDialogVisible = ref(false);
const selectedRowToDelete = ref<Assessment | null>(null);
const titleDialog = ref('');

async function onSearchUpdate(keyword: string) {
  const res = await new AssessmentService('quiz').fetchAllStUser(pagination.value, keyword);
  if (res) {
    rows.value = res.data;
    pagination.value!.rowsNumber = res.total;
  }
}

const { notify } = useQuasar();

const fetchDataRow = async (_pag: QTableProps['pagination']) => {
  const res = await new AssessmentService('quiz').fetchAllStUser(_pag, search.value);
  if (res) {
    rows.value = res.data;
    pagination.value!.rowsNumber = res.total;
  }
};

const handleRequest: QTableProps['onRequest'] = ({ pagination: _pag }) => {
  if (_pag) {
    pagination.value = _pag;
  }
  fetchDataRow(_pag).catch((error) => {
    console.error('Failed to fetch assessments:', error);
    notify({
      type: 'negative',
      message: 'เกิดข้อผิดพลาดในการดึงข้อมูลแบบทดสอบ',
    });
  });
};
async function onClickPreview(row: Assessment) {
  try {
    globalStore.setQuizTitle(row.id.toString(), row.name || `แบบทดสอบ #${row.id}`);
    await router.push({
      name: 'quiz-preview',
      params: { linkUrl: row.linkURL.toString() }, // ✅ ใช้ linkUrl param
    });
  } catch (error) {
    console.error('Navigation to preview failed:', error);
  }
}

async function onConfirmDelete() {
  if (!selectedRowToDelete.value) return;
  try {
    await new AssessmentService('quiz').deleteOne(selectedRowToDelete.value.id);
    await fetchDataRow(pagination.value);
  } catch (error) {
    console.error(error);
  } finally {
    selectedRowToDelete.value = null;
  }
}

function onCancelDelete() {
  selectedRowToDelete.value = null;
}

onMounted(() => {
  fetchDataRow(defaultPaginationValue).catch((error) => {
    console.error('Failed to fetch initial assessments:', error);
    notify({
      type: 'negative',
      message: 'เกิดข้อผิดพลาดในการดึงข้อมูลแบบทดสอบ',
    });
  });
});
</script>
<style scoped lang="scss">
:deep(.q-table thead tr) {
  background-color: var(--q-primary) !important;
  color: black !important;
}

.view-icon {
  background-color: var(--q-accent);
  color: white;
  width: 68px;
  border-radius: 12px;
}

.edit-graph-icon {
  background-color: var(--q-accent);
  color: $text-white;
  border-radius: 12px;
}

.del-icon {
  background-color: $negative;
  color: $text-white;
  border-radius: 12px;
}

:deep(.q-table thead th) {
  font-size: 20px;
}

:deep(.q-table tbody td) {
  font-size: 18px;
}
</style>

<!-- Deprecated: Use UserAssessmentPage.vue instead for unified user assessment listing. -->
