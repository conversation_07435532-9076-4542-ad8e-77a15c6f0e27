<template>
  <q-dialog ref="dialogRef" persistent @hide="onDialogHide">
    <q-card class="card-form" bordered flat style="width: 1024px">
      <q-form ref="formRef" @submit.prevent="onClickSave">
        <q-card-section class="text-h6">{{ computedTitle }}</q-card-section>
        <q-card-section class="q-gutter-y-lg">
          <q-input color="accent" filled v-model="formData.name" label="ชื่อ" />
          <q-input color="accent" filled v-model="formData.email" label="อีเมล" />
          <q-select
            color="accent"
            filled
            v-model="selectedRoles"
            :options="roleOptions"
            label="บทบาท"
            multiple
            option-label="name"
            option-value="id"
            use-chips
          />
          <q-input
            v-if="!editMode"
            filled
            v-model="formData.password"
            label="รหัสผ่าน"
            color="accent"
            type="password"
            :rules="[
              (val) => !!val || 'กรุณากรอกรหัสผ่าน',
              (val) => val.length >= 8 || 'รหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร',
            ]"
          />
          <q-input
            v-if="editMode"
            filled
            v-model="formData.currentPassword"
            label="รหัสผ่านเดิม (ถ้าต้องการเปลี่ยน)"
            color="accent"
            type="password"
          />
          <q-input
            v-if="editMode"
            filled
            v-model="formData.newPassword"
            label="รหัสผ่านใหม่ (ถ้าต้องการเปลี่ยน)"
            color="accent"
            type="password"
            :rules="[(val) => !val || val.length >= 8 || 'รหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร']"
          />
        </q-card-section>
        <q-card-actions align="right" class="q-px-md">
          <q-btn color="secondary" label="ยกเลิก" flat @click="onClickCancel" />
          <q-btn type="submit" color="accent" label="บันทึก" />
        </q-card-actions>
      </q-form>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { QForm, useDialogPluginComponent } from 'quasar';
import { RoleService } from 'src/services/ums/roleService';
import { UserService } from 'src/services/ums/userService';
import type { Role } from 'src/types/models';
import { type User } from 'src/types/models';
import { computed, onMounted, ref, watch } from 'vue';

const props = defineProps<{
  user?: User;
}>();

const { dialogRef, onDialogHide, onDialogOK, onDialogCancel } = useDialogPluginComponent();

const formRef = ref<typeof QForm>();
const computedTitle = computed(() =>
  props.user ? `แก้ไขผู้ใช้งาน: ${props.user.name}` : 'สร้างผู้ใช้งานใหม่',
);
const editMode = computed(() => !!props.user);
const formData = ref({
  ...props.user,
  roles: props.user?.roles ?? [],
});

const roleOptions = ref<Role[]>([]);
const selectedRoles = ref<Role[]>(props.user?.roles ?? []); // ใช้ array ของ object แทน
const selectedRoleIds = computed(() => selectedRoles.value.map((role) => role.id)); // คำนวณ array ของ id
// Sync formData.roles with selectedRoles
watch(
  selectedRoles,
  (newRoles) => {
    formData.value.roles = newRoles;
  },
  { immediate: true },
);

const onClickSave = async () => {
  if (!formRef.value) {
    console.error('Form reference is not set');
    return;
  }
  const isValid = await formRef.value.validate();
  if (isValid) {
    const roles = selectedRoles.value;

    onDialogOK({
      ...formData.value,
      roles,
      roleId: selectedRoleIds.value,
    });
  }
};

const onClickCancel = () => {
  onDialogCancel();
};

onMounted(async () => {
  const resRole = await RoleService.getRoles({ page: 1, rowsPerPage: 15 });
  roleOptions.value = resRole.data.data;
  if (props.user) {
    const resUser = await UserService.getUserById(props.user?.id ?? 0);
    formData.value = { ...resUser.data, roles: resUser.data.roles ?? [] };
  }
});
</script>

<style scoped>
.q-btn {
  min-width: 100px;
}
</style>
