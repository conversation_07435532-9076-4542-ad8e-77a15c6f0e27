<template>
  <q-dialog ref="dialogRef" persistent @hide="onDialogHide">
    <q-card class="card-form" bordered flat style="width: 1024px">
      <q-form ref="formRef" @submit.prevent="onClickSave()">
        <q-card-section class="text-h6"> {{ computedTitle }}</q-card-section>
        <q-card-section class="q-gutter-y-lg">
          <q-input color="accent" filled v-model="formData.name" label="ชื่อ" />
          <q-input color="accent" filled v-model="formData.description" label="คำอธิบาย" />
          <q-select
            color="accent"
            filled
            v-model="selectedPermissions"
            :options="permissionOptions"
            label="การอนุญาต"
            multiple
            option-label="name"
            option-value="id"
            use-chips
          />
        </q-card-section>
        <q-card-actions align="right" class="q-px-md">
          <q-btn color="secondary" label="ยกเลิก" flat @click="onClickCancel" />
          <q-btn type="submit" color="accent" label="บันทึก" @click="onClickSave" />
        </q-card-actions>
      </q-form>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { QForm, useDialogPluginComponent } from 'quasar';
import { PermissionService } from 'src/services/ums/permissionService';
import { RoleService } from 'src/services/ums/roleService';
import type { Permission, Role } from 'src/types/models';
import { computed, onMounted, ref, watch } from 'vue';

const props = defineProps<{
  role?: Role;
}>();

const permissionOptions = ref<Permission[]>([]);
const selectedPermissions = ref<Permission[]>(props.role?.permissions ?? []);
const selectedPermissionIds = computed(() =>
  selectedPermissions.value.map((permission) => permission.id),
);

const { dialogRef, onDialogHide, onDialogOK, onDialogCancel } = useDialogPluginComponent();

const formRef = ref<typeof QForm>();

const computedTitle = computed(() => {
  return props.role ? `แก้ไขบทบาท: ${props.role.name}` : 'สร้างบทบาทใหม่';
});

const formData = ref({
  ...props.role,
  permissions: props.role?.permissions ?? [],
});

watch(
  selectedPermissions,
  (newRole) => {
    formData.value.permissions = newRole;
  },
  { immediate: true },
);

const onClickSave = async () => {
  if (!formRef.value) {
    console.error('Form reference is not set');
    return;
  }
  const isValid = await formRef.value.validate();
  if (!isValid) return;
  const permissions = selectedPermissions.value;
  onDialogOK({
    ...formData.value,
    permissions,
    permissionIds: selectedPermissionIds.value,
  });
};

const onClickCancel = () => {
  onDialogCancel();
};

onMounted(async () => {
  const res = await PermissionService.getPermissions({ page: 1, rowsPerPage: 15 });
  permissionOptions.value = res.data.data;

  if (props.role) {
    const resUser = await RoleService.getRoleById(props.role?.id ?? 0);
    selectedPermissions.value = resUser.data.permissions ?? [];
  } else {
    selectedPermissions.value = permissionOptions.value.filter((p) => p.isDefault);
  }
});
</script>

<style scoped>
.q-btn {
  min-width: 100px;
}
</style>
