<template>
  <q-page padding>
    <q-table
      title="จัดการผู้ใช้งาน"
      v-model:pagination="pagination"
      :rows
      :columns="userColumns"
      row-key="id"
      @request="onRequest"
      separator="cell"
    >
      <template #top-right>
        <div>
          <q-btn color="accent" label="เพิ่มผู้ใช้งาน" icon="add" @click="onClickNewUser" />
        </div>
      </template>
      <template #body-cell-actions="props">
        <q-td class="row justify-center">
          <q-btn icon="edit" padding="xs" @click="onClickEdit(props.row as User)" />
          <q-btn icon="delete" padding="xs" @click="onClickDelete(props.row as User)"></q-btn>
        </q-td>
      </template>
    </q-table>
  </q-page>
</template>

<script setup lang="ts">
import { type QTableProps, useQuasar } from 'quasar';
import { defaultPaginationValue } from 'src/configs/app.config';
import { userColumns } from 'src/data/table_columns';
import { UserService } from 'src/services/ums/userService';
import type { User } from 'src/types/models';
import { defineAsyncComponent, onMounted, ref } from 'vue';

const pagination = ref({ ...defaultPaginationValue });
const rows = ref<User[]>([]);
const onRequest: QTableProps['onRequest'] = ({ pagination }) => {
  UserService.getUsers(pagination)
    .then((res) => {
      rows.value = res.data.data;
    })
    .catch((error) => {
      console.error('Error fetching users:', error);
    });
};

onMounted(() => {
  UserService.getUsers(pagination.value)
    .then((res) => {
      rows.value = res.data.data;
    })
    .catch((error) => {
      console.error('Error fetching users:', error);
    });
});

const $q = useQuasar();

const onClickNewUser = () => {
  // Logic to open the new user dialog
  $q.dialog({
    component: defineAsyncComponent(() => import('../dialog/userForm.vue')),
    persistent: true,
    componentProps: {
      user: null, // Pass null for new user
    },
  }).onOk((payload: User) => {
    // Logic to handle the new user data after dialog is closed
    if (payload) {
      UserService.createUser(payload)
        .then((res) => {
          rows.value.push(res.data); // Add the new user to the table
          $q.notify({
            type: 'positive',
            message: 'ผู้ใช้งานถูกสร้างเรียบร้อยแล้ว',
          });
        })
        .catch((error) => {
          console.error('Error creating user:', error);
          $q.notify({
            type: 'negative',
            message: 'เกิดข้อผิดพลาดในการสร้างผู้ใช้งาน',
          });
        });
    }
  });
};

const onClickEdit = (user: User) => {
  // Logic to open the edit user dialog
  $q.dialog({
    component: defineAsyncComponent(() => import('../dialog/userForm.vue')),
    persistent: true,
    componentProps: {
      user, // Pass the selected user for editing
    },
  }).onOk((payload: User) => {
    // Logic to handle the updated user data after dialog is closed
    if (payload) {
      UserService.updateUser(user.id, payload)
        .then((res) => {
          const index = rows.value.findIndex((u) => u.id === res.data.id);
          if (index !== -1) {
            rows.value[index] = res.data; // Update the user in the table
          }
          $q.notify({
            type: 'positive',
            message: 'ผู้ใช้งานถูกแก้ไขเรียบร้อยแล้ว',
          });
        })
        .catch((error) => {
          console.error('Error updating user:', error);
          $q.notify({
            type: 'negative',
            message: 'เกิดข้อผิดพลาดในการแก้ไขผู้ใช้งาน',
          });
        });
    }
  });
};

const onClickDelete = (user: User) => {
  // Logic to confirm and delete the user
  $q.dialog({
    title: 'ยืนยันการลบ',
    message: `คุณแน่ใจหรือไม่ว่าต้องการลบผู้ใช้งาน: ${user.name}?`,
    cancel: true,
    persistent: true,
  }).onOk(() => {
    UserService.deleteUser(user.id)
      .then(() => {
        rows.value = rows.value.filter((u) => u.id !== user.id); // Remove the user from the table
        $q.notify({
          type: 'positive',
          message: 'ผู้ใช้งานถูกลบเรียบร้อยแล้ว',
        });
      })
      .catch((error) => {
        console.error('Error deleting user:', error);
        $q.notify({
          type: 'negative',
          message: 'เกิดข้อผิดพลาดในการลบผู้ใช้งาน',
        });
      });
  });
};
</script>

<style scoped></style>
