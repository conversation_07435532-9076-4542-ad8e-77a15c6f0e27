<template>
  <q-page padding>
    <q-table
      title="จัดการบทบาท"
      v-model:pagination="pagination"
      :rows
      :columns="roleColumns"
      row-key="id"
      @request="onRequest"
      separator="cell"
    >
      <template #top-right>
        <div>
          <q-btn color="accent" label="เพิ่มบทบาท" icon="add" @click="onClickNewUser" />
        </div>
      </template>
      <template #body-cell-actions="props">
        <q-td class="row justify-center">
          <q-btn icon="edit" padding="xs" @click="onClickEdit(props.row as Role)" />
          <q-btn icon="delete" padding="xs" @click="onClickDelete(props.row as Role)"></q-btn>
        </q-td>
      </template>
    </q-table>
  </q-page>
</template>

<script setup lang="ts">
import { type QTableProps, useQuasar } from 'quasar';
import { defaultPaginationValue } from 'src/configs/app.config';
import { roleColumns } from 'src/data/table_columns';
import { RoleService } from 'src/services/ums/roleService';
import type { Role } from 'src/types/models';
import { defineAsyncComponent, onMounted, ref } from 'vue';

const pagination = ref({ ...defaultPaginationValue });
const rows = ref<Role[]>([]);
const onRequest: QTableProps['onRequest'] = ({ pagination }) => {
  RoleService.getRoles(pagination)
    .then((res) => {
      rows.value = res.data.data;
    })
    .catch((error) => {
      console.error('Error fetching roles:', error);
    });
};

onMounted(() => {
  RoleService.getRoles(pagination.value)
    .then((res) => {
      rows.value = res.data.data || [];
    })
    .catch((error) => {
      console.error('Error fetching roles:', error);
    });
});

const $q = useQuasar();

const onClickNewUser = () => {
  $q.dialog({
    component: defineAsyncComponent(() => import('../dialog/roleForm.vue')),
    persistent: true,
    componentProps: {
      role: null, // Pass null for new role
    },
  }).onOk((payload: Role) => {
    if (payload) {
      RoleService.createRole(payload)
        .then((res) => {
          rows.value.push(res.data); // Add the new role to the table
          $q.notify({
            type: 'positive',
            message: 'บทบาทถูกสร้างเรียบร้อยแล้ว',
          });
        })
        .catch((error) => {
          console.error('Error creating role:', error);
          $q.notify({
            type: 'negative',
            message: 'เกิดข้อผิดพลาดในการสร้างบทบาท',
          });
        });
    }
  });
};

const onClickEdit = (role: Role) => {
  $q.dialog({
    component: defineAsyncComponent(() => import('../dialog/roleForm.vue')),
    persistent: true,
    componentProps: {
      role, // Pass the selected role for editing
    },
  }).onOk((payload: Role) => {
    if (payload) {
      RoleService.updateRole(role.id, payload)
        .then((res) => {
          const index = rows.value.findIndex((r) => r.id === res.data.id);
          if (index !== -1) {
            rows.value[index] = res.data; // Update the role in the table
          }
          $q.notify({
            type: 'positive',
            message: 'บทบาทถูกแก้ไขเรียบร้อยแล้ว',
          });
        })
        .catch((error) => {
          console.error('Error updating role:', error);
          $q.notify({
            type: 'negative',
            message: 'เกิดข้อผิดพลาดในการแก้ไขบทบาท',
          });
        });
    }
  });
};

const onClickDelete = (role: Role) => {
  $q.dialog({
    title: 'ยืนยันการลบ',
    message: `คุณแน่ใจหรือไม่ว่าต้องการลบบทบาท: ${role.name}?`,
    cancel: true,
    persistent: true,
  }).onOk(() => {
    RoleService.deleteRole(role.id)
      .then(() => {
        rows.value = rows.value.filter((r) => r.id !== role.id); // Remove the role from the table
        $q.notify({
          type: 'positive',
          message: 'บทบาทถูกลบเรียบร้อยแล้ว',
        });
      })
      .catch((error) => {
        console.error('Error deleting role:', error);
        $q.notify({
          type: 'negative',
          message: 'เกิดข้อผิดพลาดในการลบบทบาท',
        });
      });
  });
};
</script>

<style scoped></style>
