name: NODEJS DEPLOY DEVELOP

on:
  pull_request:
    branches:
      - uat
    types: [closed]

jobs:
# วิเคราะห์คุณภาพโค้ด (Sonarqube) ------------------------------------------------------------------------------------------------
  sonarqube_scan:
    name: "วิเคราะห์คุณภาพโค้ด (Sonarqube)"
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
    - name: "วิเคราะห์โค้ดด้วยเครื่องมือ SonarQube"
      uses: sonarsource/sonarqube-scan-action@v4
      env:
        SONAR_TOKEN: ${{ vars.SONAR_TOKEN }}
        SONAR_HOST_URL: ${{ vars.SONAR_HOST_URL }}

# สร้างบิลด์และสแกนความปลอดภัย ---------------------------------------------------------------------------------------------------
  security_scan:
    name: "สร้างบิ้วด์และสแกนความปลอดภัย"
    runs-on: ubuntu-latest

    steps:
      - name: "นำโค้ดออกมา"
        uses: actions/checkout@v4

      - name: "ติดตั้ง Nodejs"
        uses: actions/setup-node@v4
        with:
          node-version: ${{ vars.NODE_VERSION }}

      - name: "ติดตั้ง dependencies"
        run: npm install

      - name: "ตรวจสอบช่องโหว่ด้านความปลอดภัย (security vulnerabilities)"
        run: npm audit --audit-level=high

# สร้างบิลด์และนำไป Deploy ขึ้นเครื่อง Develop ----------------------------------------------------------------------------------------------
  build_and_deploy:
    name: "สร้างบิลด์และนำไป Deploy ขึ้นเครื่อง Develop"
    runs-on: ubuntu-latest
    env:
      TZ: 'Asia/Bangkok'
    steps:
      - name: "Set Timezone"
        run: |
          sudo ln -sf /usr/share/zoneinfo/Asia/Bangkok /etc/localtime
          echo "Timezone set to: $(date)"

      - name: "นำโค้ดออกมา"
        uses: actions/checkout@v4

# การแก้ไขค่าตั้งค่า (Configuration) -------------------------------------------------------------------------------------------------------
#      - name: "การแก้ไขค่าตั้งค่า (Configuration)"
#        run: |
#          sed -i 's|http://localhost|https://test-dev.buu.ac.th|g' ${{ github.workspace }}/config/app.php

      - name: "แก้ไขการตั้งค่า API endpoints"
        run: |
          sed -i 's|http://localhost:3000|https://intern-back-dev.buu.ac.th|g' ${{ github.workspace }}/.env

      - name: "แก้ไขการตั้งค่า Path ของระบบ"
        run: |
          sed -i 's|http://localhost:9000|https://intern-dev.buu.ac.th|g' ${{ github.workspace }}/src/components/common/ShareLinkDialog.vue

      # - name: "แก้ไขการตั้งค่าใน cypress.config.ts" # ตอนนี้ยังไม่ได้ใช้
      #   run: |
      #     sed -i 's|http://localhost:3000|https://intern-back-dev.buu.ac.th|g' ${{ github.workspace }}/cypress.config.ts && \
      #     sed -i 's|http://localhost:9000|https://intern-dev.buu.ac.th|g' ${{ github.workspace }}/cypress.config.ts

# คัดลอกไฟล์ไปที่ Develop server ---------------------------------------------------------------------------------------------------------
      - name: "ติดตั้ง dependencies"
        run: |
          npm install
          npm install axios form-data
          npm install -g @quasar/cli

      # - name: "Run Type Check with Increased Memory" # ตอนนี้ยังไม่ได้ใช้
      #   run: node --max-old-space-size=4096 node_modules/.bin/tsc --noEmit

      - name: "สร้างบิลด์"
        run: |
          export NODE_OPTIONS="--max_old_space_size=8192"
          quasar build

      - name: "ตรวจสอบไฟล์ที่สร้างขึ้น"
        run: |
          ls -la dist/spa/
          echo "Checking if index.html exists:"
          test -f dist/spa/index.html && echo "index.html exists" || echo "index.html missing"

      - name: "คัดลอกไฟล์ไปที่ Develop server"
        uses: appleboy/scp-action@v1
        with:
          host: ${{ vars.SSH_DEVELOP }}
          username: develop
          port: 22
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          source: "*,!node_modules/*"
          target: /home/<USER>/${{ vars.PROJECT_NAME }}/public_html

# ติดตั้งแอปพลิเคชันบน Develop server -----------------------------------------------------------------------------------------------------
      - name: "ติดตั้งแอปพลิเคชันบน Develop server"
        uses: appleboy/ssh-action@v1
        with:
          host: ${{ vars.SSH_DEVELOP }}
          username: develop
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          port: 22
          script: |
            cp /home/<USER>/k3s-temp-front/* /home/<USER>/${{ vars.PROJECT_NAME }}
            cd /home/<USER>/${{ vars.PROJECT_NAME }}
            # Set proper permissions
            chmod -R 755 public_html
            chown -R develop:develop public_html
            # Verify deployment
            ls -la public_html/
            echo "Checking nginx configuration:"
            nginx -t
            # Apply Kubernetes configurations
            export KUBECONFIG=~/.kube/config
            sed -i 's|K8S_PROJECT_NAME|${{ vars.PROJECT_NAME }}|g' deployment-k3s.yml
            sed -i 's|K8S_NGINX_DOCUMENT_ROOT|${{ vars.NGINX_DOCUMENT_ROOT }}|g' deployment-k3s.yml
            sed -i 's|K8S_PROJECT_NAME|${{ vars.PROJECT_NAME }}|g' service-k3s.yml
            sed -i 's|K8S_PROJECT_NAME|${{ vars.PROJECT_NAME }}|g' ingress-k3s.yml
            kubectl apply -f deployment-k3s.yml
            kubectl apply -f service-k3s.yml
            kubectl apply -f ingress-k3s.yml
            # Verify pod status
            kubectl get pods -n ${{ vars.PROJECT_NAME }}
            # Clean up
            rm -f deployment-k3s.yml
            rm -f service-k3s.yml
            rm -f ingress-k3s.yml

# ตอนนี้ยังไม่ได้ใช้
# # ติดตั้ง Cypress และทำการรัน Automated Test -----------------------------------------------------------------------------------------------------
#       - name: "ติดตั้ง Cypress"
#         run: npm install cypress --save-dev

#       - name: "ติดตั้ง Cypress Dependencies"
#         run: |
#           apt update
#           apt install -y libgtk2.0-0 libgtk-3-0 libgbm-dev libnotify-dev libnss3 libxss1 libasound2t64 libxtst6 xauth xvfb

#       - name: "ติดตั้ง Java"
#         uses: actions/setup-java@v4
#         with:
#           distribution: 'zulu'
#           java-version: '17'

#       - name: "ติดตั้ง Allure Report"
#         run: |
#           npm install -g allure-commandline
#           npm install --save-dev allure-cypress

#       - name: "Cypress Test"
#         id: cypress_test
#         continue-on-error: true # Allow failure, but workflow continues
#         # run: npx cypress run
#         run: |
#           npx cypress run --headless --spec "cypress/e2e/integration/**/**/*.cy.ts" || echo $? > test_exit_code.txt
#           npx cypress run --headless --spec "cypress/e2e/Smoke/*.cy.ts" || echo $? >> test_exit_code.txt
#           npx cypress run --headless --spec "cypress/e2e/F1/**/*.cy.ts" || echo $? >> test_exit_code.txt

# # การออก Report และส่งข้อมูลไปเก็บที่ API Storage -----------------------------------------------------------------------------------------------------
#       - name: "Generate Report"
#         run: allure generate allure-results --clean -o allure-report

#       - uses: actions/upload-artifact@v3
#         with:
#           name: "Cypress-Report"
#           path: ${{ github.workspace }}/cypress/results/*
#           if-no-files-found: ignore
#           retention-days: 7

#       - uses: actions/upload-artifact@v3
#         with:
#           name: "Allure-Result"
#           path: ${{ github.workspace }}/allure-results/*
#           if-no-files-found: ignore
#           retention-days: 7

#       - uses: actions/upload-artifact@v3
#         with:
#           name: "Allure-Report"
#           path: ${{ github.workspace }}/allure-report/*
#           if-no-files-found: ignore
#           retention-days: 7

#       - name: "Compress Allure Report"
#         run: |
#           zip -r allure-report.zip allure-report/

#       - name: "Upload Allure Report to API Storage"
#         id: upload_report
#         run: |
#           FILENAME=allure-report-$(date +%F-%H-%M)
#           echo "Original File Name: $FILENAME"

#           RESPONSE=$(curl -X POST "https://intern-back-dev.buu.ac.th/api/uploadFile" \
#             -H "Content-Type: multipart/form-data" \
#             -F "path=allure-reports/intern" \
#             -F "fileName=$FILENAME" \
#             -F "fileType=zip" \
#             -F "file=@allure-report.zip" \
#             -H "Accept: application/json")

#           echo "Response from API: $RESPONSE"

#           # Extract the URL from the JSON response (assuming JSON format: {"result": "test_reports/allure/xxx.zip"})
#           VIEW_URL=$(echo $RESPONSE | jq -r ".result")

#           # Save it as an environment variable for later use
#           echo "VIEW_URL=$VIEW_URL" >> $GITHUB_ENV

#       - name: "Display Allure Report View URL"
#         run: |
#           echo "✅ Allure Report is available at: $VIEW_URL"

# # ตรวจสอบว่าถ้าการรัน Automated Test ล้มเหลวจะทำการยกเลิกการ Deploy ขึ้น Develop server -----------------------------------------------------------------------------------------------------
#       - name: "Fail Job If Any Cypress Tests Failed"
#         run: |
#           if grep -q '[^0]' test_exit_code.txt; then exit 1; fi
