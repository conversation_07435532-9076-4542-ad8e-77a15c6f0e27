describe('Quiz Edit', () => {
  beforeEach(() => {
    cy.fixture('users').then((user) => {
      cy.login(user.superAdmin.username, user.superAdmin.password);
    });
    cy.enterQuiz();
    cy.searchQuizFormName('ฟอร์มไว้สำหรับ Test Cypress');
  });

  it('TC-03-05-01 edit quiz เมื่อกดปุ่ม edit จะเข้าไปหน้า edit', () => {
    cy.get(':nth-child(6) > .q-gutter-x-sm > :nth-child(2)').should('be.visible');
    cy.wait(2000);
    cy.get(':nth-child(6) > .q-gutter-x-sm > :nth-child(2)').click();
    cy.url().should('include', 'quiz/1307/edit#questions');
  });
});
