describe('Quiz Preview', () => {
  beforeEach(() => {
    cy.fixture('users').then((user) => {
      cy.login(user.superAdmin.username, user.superAdmin.password);
    });
    cy.enterQuiz();
    cy.searchQuizFormName('ฟอร์มไว้สำหรับ Test Cypress');
  });

  it('TC-03-04-01 preview quiz', () => {
    cy.get('.q-table__container').should('be.visible');
    cy.get(':nth-child(1) > :nth-child(6) > .q-gutter-x-sm > .view-icon').click();
    cy.wait(1000);
    cy.url().should('include', '/quiz/68e942fd-772e-43b9-a97b-9748877ff97e/preview');
  });
});
