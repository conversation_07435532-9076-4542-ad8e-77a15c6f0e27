describe('Quiz Share Link', () => {
  beforeEach(() => {
    cy.fixture('users').then((user) => {
      cy.login(user.superAdmin.username, user.superAdmin.password);
    });
    cy.enterQuiz();
  });

  it('TC-03-03-01 share link', () => {
    cy.get('.q-table__container').should('be.visible');
    cy.get(':nth-child(1) > :nth-child(5) > .q-icon').click();
    cy.get('.q-card').should('be.visible');
    cy.get('.q-img__image').should('be.visible');
    cy.get('[data-cy="shareLinkDialogLink"]').should('be.visible');
    cy.get('.bg-positive').click();
    cy.get('.q-card').should('not.be.visible');
  });
});
