describe('Quiz Manage Screen', () => {
  beforeEach(() => {
    cy.fixture('users').then((user) => {
      cy.login(user.superAdmin.username, user.superAdmin.password);
    });
    cy.enterQuiz();
  });

  it('TC-03-01-01 main menu > quiz management', () => {
    cy.get('.q-table__container').should('be.visible');
  });

  it('TC-03-01-02 bread crump quiz management > main menu', () => {
    cy.get('.text-primary > .q-breadcrumbs__el').click();
    cy.url().should('include', '/home');
  });

  it('TC-03-01-03 search quiz name', () => {
    cy.get('.q-gutter-sm > .q-field > .q-field__inner > .q-field__control').type('ง่วง');
    cy.get(':nth-child(1) > [style="min-width: 250px; white-space: normal;"]').should(
      'contain',
      'ง่วง',
    );
  });

  it('TC-03-01-04 check pagination', () => {
    cy.get('[aria-label="Next page"] > .q-btn__content > .q-icon').click();
    cy.intercept('GET', '/assessments?sortBy=id&order=ASC&limit=10&page=2&type=quiz');
    cy.get('.q-table__container').should('be.visible');
    cy.get('[aria-label="Previous page"] > .q-btn__content > .q-icon').click();
    cy.get('.q-table__container').should('be.visible');
    cy.intercept('GET', '/assessments?sortBy=id&order=ASC&limit=10&page=1&type=quiz');
  });
});
