describe('Quiz Get Data Edit Page', () => {
  beforeEach(() => {
    cy.fixture('users').then((user) => {
      cy.login(user.superAdmin.username, user.superAdmin.password);
    });
    cy.enterQuiz();
    cy.searchQuizFormName('ฟอร์มไว้สำหรับ Test Cypress');
  });

  it('TC-04-01-01 view quiz', () => {
    cy.get(':nth-child(6) > .q-gutter-x-sm > :nth-child(2)').should('be.visible');
    cy.wait(2000);
    cy.get(':nth-child(6) > .q-gutter-x-sm > :nth-child(2)').click();
    cy.get(':nth-child(2) > [data-v-cdcd3b8e=""] > .input-wrapper > .editable-div').should(
      'contain',
      'test',
    );
    cy.get(':nth-child(3) > [data-v-cdcd3b8e=""] > .input-wrapper > .editable-div').should(
      'contain',
      'tests',
    );
    cy.get('[data-v-f912b43d=""] > .input-wrapper > .editable-div').should('contain', 'question1');
    cy.get(
      ':nth-child(2) > :nth-child(1) > .block-container > .block-content > .q-card > .q-ml-md > .q-mb-sm > :nth-child(3) > .q-field > .q-field__inner > .q-field__control > .q-field__control-container > [data-cy="option-text-input-0"]',
    ).should('have.value', 'option2');
  });
});
