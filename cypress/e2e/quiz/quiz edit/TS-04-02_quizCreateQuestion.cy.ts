describe('Quiz Create Question', () => {
  beforeEach(() => {
    cy.fixture('users').then((user) => {
      cy.login(user.superAdmin.username, user.superAdmin.password);
    });
    cy.enterQuiz();
    cy.searchQuizFormName('ฟอร์มไว้สำหรับ Test Cypress');
  });

  it('TC-04-02-01 create question', () => {
    cy.get(':nth-child(6) > .q-gutter-x-sm > :nth-child(2)').should('be.visible');
    cy.wait(2000);
    cy.get(':nth-child(6) > .q-gutter-x-sm > :nth-child(2)').click();
    cy.get(
      ':nth-child(1) > .fixed-fab-col > .q-card > div > :nth-child(1) > .q-btn__content > .q-icon > img',
    ).click();
    cy.get(':nth-child(3) > :nth-child(1) > .block-container > .block-content > .q-card').should(
      'be.visible',
    );
  });
});
